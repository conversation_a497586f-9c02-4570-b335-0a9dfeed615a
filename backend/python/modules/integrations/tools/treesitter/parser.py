from pathlib import Path
from typing import Dict, List, Optional, Tuple
from tree_sitter import Node, Parser, Query, QueryCursor
from tree_sitter_language_pack import get_parser

from core.config import ChunkConfig
from modules.common.constants import LanguageEnum
from modules.common.schema import Chunk
from utils.trace_logger import get_trace_logger


trace_logger = get_trace_logger(__name__)

class FileParser:
    """Manages tree-sitter parsers for different languages."""
    def __init__(self, chunk_config: ChunkConfig):
        self.language_parsers: Dict[LanguageEnum, Parser] = {}
        self.language_queries: Dict[LanguageEnum, Query] = {}
        self.config = chunk_config

    def parse(self, file_path: str, file_content: str) -> Tuple[Dict[int, str], List[Chunk]]:
        # 根据文件后缀名选择对应的解析器
        file_extension = '.' + file_path.split('.')[-1] if '.' in file_path else file_path.split('.')[-1]
        language_enum = LanguageEnum.from_suffix(file_extension)

        if language_enum is None:
            return {}, []
        
        # 加载parser
        parser = None
        if language_enum in self.language_parsers:
            parser = self.language_parsers[language_enum]
        else:
            try:
                parser = get_parser(language_enum.value[0])
                self.language_parsers[language_enum] = parser
            except Exception as e:
                self.language_parsers[language_enum] = None
                trace_logger.info(f"Failed to load parser for {language_enum}: {e}")
                return {}, []

        # 加载query
        query = None
        if language_enum in self.language_queries:
            query = self.language_queries[language_enum]
        else:
            query_content = self._load_query(language_enum)
            if not query_content:
                self.language_queries[language_enum] = None
                trace_logger.info(f"Failed to load query for {language_enum}")
                return {}, []

            try:
                query = Query(parser.language, query_content)
                self.language_queries[language_enum] = query
            except Exception as e:
                self.language_queries[language_enum] = None
                trace_logger.info(f"Failed to load query for {language_enum}: {e}")
                return {}, []

        if parser is None or query is None:
            return {}, []
        
        # 解析文件
        tree = parser.parse(file_content.encode("utf-8"))
        query_cursor = QueryCursor(query)
        captures = query_cursor.captures(tree.root_node)

        # 解析文件结构
        definitions: List[Tuple[Node, str]] = []
        processed_nodes = set()
        for capture_name, nodes in captures.items():
            if "definition" in capture_name:
                for node in nodes:
                    node_key = (node.start_point, node.end_point)
                    if node_key not in processed_nodes:
                        definitions.append((node, capture_name))
                        processed_nodes.add(node_key)

        definitions.sort(key=lambda x: (x[0].start_point[0], -x[0].end_point[0])) # 如果一个结构匹配到多个definition，取最长的那一个，较短的会被忽略

        lines = file_content.splitlines()
        file_chunks = []
        key_structure_lines: Dict[int, str] = {}
        
        seen_lines = set()
        seen_min_start_line, seen_max_end_line = 0, 0
        prev_nodes: List[Node] = []

        for node, capture_name in definitions:
            if node.start_point[0] in seen_lines: # 已经被处理过了
                continue

            seen_lines.add(node.start_point[0])
            
            # 关键行可能会在已经出现处理过的chunk当中出现，因此其位置在seen_min_start_line和seen_max_end_line更新之前
            if self._is_keynode(capture_name):
                key_structure_lines[node.start_point[0]] = f"{capture_name}: {lines[node.start_point[0]].strip()}"
            
            if node.start_point[0] >= seen_min_start_line and node.end_point[0] <= seen_max_end_line: # 已经被处理过了
                continue
            seen_min_start_line = min(seen_min_start_line, node.start_point[0])
            seen_max_end_line = max(seen_max_end_line, node.end_point[0])

            node_line_cnt = node.end_point[0] - node.start_point[0] + 1
            
            # 如果当前节点超大
            if node_line_cnt > self.config.max_chunk_size:
                # 清理prev_nodes
                if prev_nodes:
                    file_chunks.append(
                        Chunk(
                            file_path=file_path,
                            start_line=prev_nodes[0].start_point[0],
                            end_line=prev_nodes[-1].end_point[0],
                            content="\n".join(lines[prev_nodes[0].start_point[0]: prev_nodes[-1].end_point[0] + 1]),
                        )
                    )
                    prev_nodes = []
                    
                # 将当前节点加入到chunk中
                file_chunks.append(
                    Chunk(
                        file_path=file_path,
                        start_line=node.start_point[0],
                        end_line=node.end_point[0],
                        content="\n".join(lines[node.start_point[0]: node.end_point[0] + 1]),
                    )
                )
                for seg_idx, seg_line in enumerate(range(node.start_point[0], node.end_point[0], self.config.max_chunk_size)):
                    start_line = seg_line
                    end_line = min(seg_line + self.config.max_chunk_size, node.end_point[0])
                    content = "\n".join(lines[start_line: end_line + 1])
                    if seg_idx != 0:
                        content = lines[node.start_point[0]] + "\n...\n" + content
                    if seg_idx != (node.end_point[0] - node.start_point[0]) // self.config.max_chunk_size:
                        content += "\n...\n" + lines[node.end_point[0]]
                    
                    file_chunks.append(
                        Chunk(
                            file_path=file_path,
                            start_line=start_line,
                            end_line=end_line,
                            content=content,
                        )
                    )

                continue
            
            # 如果没有前置节点
            if not prev_nodes:
                if node_line_cnt >= self.config.min_chunk_size and node_line_cnt <= self.config.max_chunk_size:
                    file_chunks.append(
                        Chunk(
                            file_path=file_path,
                            start_line=node.start_point[0],
                            end_line=node.end_point[0],
                            content="\n".join(lines[node.start_point[0]: node.end_point[0] + 1]),
                        )
                    )
                else:
                    prev_nodes.append(node)
                continue
            
            # 如果有前置节点
            if prev_nodes:
                if node.end_point[0] - prev_nodes[0].start_point[0] >= self.config.min_chunk_size:
                    if node.end_point[0] - prev_nodes[0].start_point[0] <= self.config.max_chunk_size:
                        file_chunks.append(
                            Chunk(
                                file_path=file_path,
                                start_line=prev_nodes[0].start_point[0],
                                end_line=node.end_point[0],
                                content="\n".join(lines[prev_nodes[0].start_point[0]: node.end_point[0] + 1]),
                            )
                        )
                        prev_nodes = []
                    else:
                        file_chunks.append(
                            Chunk(
                                file_path=file_path,
                                start_line=prev_nodes[0].start_point[0],
                                end_line=prev_nodes[-1].end_point[0],
                                content="\n".join(lines[prev_nodes[0].start_point[0]: prev_nodes[-1].end_point[0] + 1]),
                            )
                        )
                        prev_nodes = [node]
                else:
                    prev_nodes.append(node)
                
        # 处理最后一个节点
        if prev_nodes:
            file_chunks.append(
                Chunk(
                    file_path=file_path,
                    start_line=prev_nodes[0].start_point[0],
                    end_line=prev_nodes[-1].end_point[0],
                    content="\n".join(lines[prev_nodes[0].start_point[0]: prev_nodes[-1].end_point[0] + 1]),
                )
            )
        
        return key_structure_lines, file_chunks
    
    def _load_query(self, language_enum: LanguageEnum) -> Optional[str]:
        """Load query string for a language."""
        # Try to load from queries directory
        query_file = Path(__file__).parent / "queries" / f"{language_enum.value[0]}.scm"
        if query_file.exists():
            try:
                return query_file.read_text(encoding="utf-8")
            except Exception as e:
                trace_logger.warning(f"读取查询文件 {query_file} 失败: {e}")

        return None
    
    def _is_keynode(self, capture_name: str) -> bool:
        """判断当前节点是否为关键节点"""
        # Determine entry type based on capture name
        key_patterns = [
            "definition.class",
            "definition.interface",
            "definition.struct",
            "definition.union",
            "definition.enum",
            "definition.function",
            "definition.method",
            "definition.constructor",
            "definition.destructor",
            "definition.operator",
            "definition.namespace",
            "definition.template"
        ]

        return any(pattern in capture_name for pattern in key_patterns)

