#!/usr/bin/env python3
"""
生成FileParser测试结果并保存到对应的测试样本目录中。
这样可以手动检查解析输出是否符合预期。
"""

import sys
import json
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Add the backend/python directory to the Python path
backend_python_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_python_dir))

from core.config import ChunkConfig
from modules.integrations.tools.treesitter.parser import FileParser
from modules.common.schema import Chunk


def chunk_to_dict(chunk: Chunk) -> Dict[str, Any]:
    """将Chunk对象转换为字典格式以便序列化。"""
    return {
        "file_path": chunk.file_path,
        "start_line": chunk.start_line,
        "end_line": chunk.end_line,
        "content": chunk.content,
        "line_count": chunk.end_line - chunk.start_line + 1
    }


def generate_parse_results():
    """为所有支持的语言生成解析结果。"""
    print("🔄 生成FileParser测试结果...")
    
    # 创建解析器配置
    chunk_config = ChunkConfig(
        name="test",
        min_chunk_size=10,
        max_chunk_size=50,
        overflow_size=5
    )
    parser = FileParser(chunk_config)
    
    # 测试资源目录
    resources_dir = Path(__file__).parent / "resources"
    
    # 支持的语言和对应的样例文件
    language_files = {
        "python": "sample.py",
        "java": "Sample.java",
        "javascript": "sample.js",
        "typescript": "sample.ts",
        "c": "sample.c",
        "cpp": "sample.cpp",
        "go": "sample.go",
        "rust": "sample.rs",
        "php": "sample.php",
        "ruby": "sample.rb",
        "swift": "sample.swift",
        "kotlin": "sample.kt",
        "scala": "sample.scala",
        "html": "sample.html",
        "css": "sample.css",
        "jsx": "sample.jsx",
        "tsx": "sample.tsx"
    }
    
    results_summary = {
        "generated_at": datetime.now().isoformat(),
        "parser_config": {
            "min_chunk_size": chunk_config.min_chunk_size,
            "max_chunk_size": chunk_config.max_chunk_size,
            "overflow_size": chunk_config.overflow_size
        },
        "languages": {}
    }
    
    for language, filename in language_files.items():
        print(f"\n📝 处理 {language}...")
        
        # 检查样例文件是否存在
        language_dir = resources_dir / language
        sample_file = language_dir / filename
        
        if not sample_file.exists():
            print(f"   ⚠️  样例文件不存在: {sample_file}")
            continue
        
        try:
            # 读取样例文件内容
            with open(sample_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析文件
            key_lines, chunks = parser.parse(str(sample_file), content)
            
            # 准备结果数据
            result_data = {
                "file_info": {
                    "path": str(sample_file),
                    "filename": filename,
                    "content_length": len(content),
                    "line_count": len(content.splitlines())
                },
                "parsing_results": {
                    "key_structure_lines": dict(key_lines),
                    "key_structure_count": len(key_lines),
                    "chunks": [chunk_to_dict(chunk) for chunk in chunks],
                    "chunk_count": len(chunks)
                },
                "analysis": {
                    "detected_structures": list(set(key_lines.values())),
                    "structure_types_count": len(set(key_lines.values())),
                    "total_lines_in_chunks": sum(chunk.end_line - chunk.start_line + 1 for chunk in chunks),
                    "coverage_percentage": (sum(chunk.end_line - chunk.start_line + 1 for chunk in chunks) / len(content.splitlines()) * 100) if content.splitlines() else 0
                }
            }
            
            # 保存结果到JSON文件
            result_file = language_dir / "parse_result.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)
            
            # 生成人类可读的报告
            report_file = language_dir / "parse_report.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"FileParser 解析结果报告 - {language.upper()}\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"文件信息:\n")
                f.write(f"  路径: {sample_file}\n")
                f.write(f"  文件名: {filename}\n")
                f.write(f"  内容长度: {len(content)} 字符\n")
                f.write(f"  行数: {len(content.splitlines())}\n\n")
                
                f.write(f"关键结构行 ({len(key_lines)} 个):\n")
                if key_lines:
                    for line_num, structure_type in sorted(key_lines.items()):
                        # 获取该行的实际内容
                        lines = content.splitlines()
                        line_content = lines[line_num] if line_num < len(lines) else ""
                        f.write(f"  第 {line_num:3d} 行: {structure_type}\n")
                        f.write(f"           内容: {line_content.strip()}\n")
                else:
                    f.write("  无检测到关键结构\n")
                
                f.write(f"\n检测到的结构类型:\n")
                structure_types = set(key_lines.values())
                if structure_types:
                    for structure_type in sorted(structure_types):
                        count = sum(1 for st in key_lines.values() if st == structure_type)
                        f.write(f"  - {structure_type}: {count} 个\n")
                else:
                    f.write("  无结构类型检测到\n")
                
                f.write(f"\n代码块信息 ({len(chunks)} 个):\n")
                if chunks:
                    for i, chunk in enumerate(chunks):
                        f.write(f"  块 {i+1}: 第 {chunk.start_line}-{chunk.end_line} 行 ({chunk.end_line - chunk.start_line + 1} 行)\n")
                        # 显示块的前几行内容
                        chunk_lines = chunk.content.splitlines()
                        preview_lines = chunk_lines[:3]
                        for j, line in enumerate(preview_lines):
                            f.write(f"    {chunk.start_line + j:3d}: {line}\n")
                        if len(chunk_lines) > 3:
                            f.write(f"    ... (还有 {len(chunk_lines) - 3} 行)\n")
                        f.write("\n")
                else:
                    f.write("  无代码块生成\n")
                
                f.write(f"\n统计信息:\n")
                f.write(f"  覆盖率: {result_data['analysis']['coverage_percentage']:.1f}%\n")
                f.write(f"  块中总行数: {result_data['analysis']['total_lines_in_chunks']}\n")
                f.write(f"  结构类型数: {result_data['analysis']['structure_types_count']}\n")
            
            # 更新总结
            results_summary["languages"][language] = {
                "success": True,
                "key_structures": len(key_lines),
                "chunks": len(chunks),
                "structure_types": list(set(key_lines.values())),
                "coverage": result_data['analysis']['coverage_percentage']
            }
            
            print(f"   ✅ 成功生成结果文件:")
            print(f"      - {result_file}")
            print(f"      - {report_file}")
            print(f"   📊 检测到 {len(key_lines)} 个关键结构, {len(chunks)} 个代码块")
            
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")
            results_summary["languages"][language] = {
                "success": False,
                "error": str(e)
            }
    
    # 保存总结报告
    summary_file = resources_dir / "parse_results_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, indent=2, ensure_ascii=False)
    
    # 生成总结报告
    summary_report = resources_dir / "parse_results_summary.txt"
    with open(summary_report, 'w', encoding='utf-8') as f:
        f.write("FileParser 解析结果总结报告\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"生成时间: {results_summary['generated_at']}\n")
        f.write(f"解析器配置: {results_summary['parser_config']}\n\n")
        
        successful = [lang for lang, result in results_summary["languages"].items() if result.get("success")]
        failed = [lang for lang, result in results_summary["languages"].items() if not result.get("success")]
        
        f.write(f"成功解析的语言 ({len(successful)}):\n")
        for lang in successful:
            result = results_summary["languages"][lang]
            f.write(f"  ✅ {lang:12s}: {result['key_structures']:2d} 结构, {result['chunks']:2d} 块, {result['coverage']:5.1f}% 覆盖率\n")
        
        if failed:
            f.write(f"\n解析失败的语言 ({len(failed)}):\n")
            for lang in failed:
                result = results_summary["languages"][lang]
                f.write(f"  ❌ {lang:12s}: {result.get('error', '未知错误')}\n")
        
        f.write(f"\n结构类型统计:\n")
        all_structures = set()
        for lang, result in results_summary["languages"].items():
            if result.get("success"):
                all_structures.update(result.get("structure_types", []))
        
        for structure in sorted(all_structures):
            langs_with_structure = [
                lang for lang, result in results_summary["languages"].items()
                if result.get("success") and structure in result.get("structure_types", [])
            ]
            f.write(f"  - {structure}: {len(langs_with_structure)} 种语言\n")
    
    print(f"\n📋 总结:")
    print(f"   ✅ 成功解析: {len(successful)} 种语言")
    if failed:
        print(f"   ❌ 解析失败: {len(failed)} 种语言")
    print(f"   📄 总结报告: {summary_file}")
    print(f"   📄 总结文本: {summary_report}")
    print(f"\n🎉 所有结果已保存到 tests/resources/ 目录中!")


if __name__ == "__main__":
    generate_parse_results()
