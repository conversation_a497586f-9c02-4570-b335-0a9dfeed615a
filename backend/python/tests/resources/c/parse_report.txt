FileParser 解析结果报告 - C
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c
  文件名: sample.c
  内容长度: 4685 字符
  行数: 247

关键结构行 (21 个):
  第  16 行: definition.struct: typedef struct {
           内容: typedef struct {
  第  21 行: definition.struct: typedef struct Node {
           内容: typedef struct Node {
  第  26 行: definition.enum: typedef enum {
           内容: typedef enum {
  第  32 行: definition.union: typedef union {
           内容: typedef union {
  第  44 行: definition.function: int add(int a, int b);
           内容: int add(int a, int b);
  第  45 行: definition.function: void print_array(int arr[], int size);
           内容: void print_array(int arr[], int size);
  第  47 行: definition.function: void free_point(Point* p);
           内容: void free_point(Point* p);
  第  50 行: definition.function: int add(int a, int b) {
           内容: int add(int a, int b) {
  第  54 行: definition.function: int subtract(int a, int b) {
           内容: int subtract(int a, int b) {
  第  58 行: definition.function: void print_array(int arr[], int size) {
           内容: void print_array(int arr[], int size) {
  第  75 行: definition.function: void free_point(Point* p) {
           内容: void free_point(Point* p) {
  第  82 行: definition.function: static int get_next_id(void) {
           内容: static int get_next_id(void) {
  第  88 行: definition.function: inline int max(int a, int b) {
           内容: inline int max(int a, int b) {
  第  93 行: definition.function: int factorial(int n) {
           内容: int factorial(int n) {
  第 103 行: definition.function: int sum_all(int count, ...) {
           内容: int sum_all(int count, ...) {
  第 117 行: definition.function: void swap(int* a, int* b) {
           内容: void swap(int* a, int* b) {
  第 127 行: definition.function: void process_matrix(int matrix[][3], int rows) {
           内容: void process_matrix(int matrix[][3], int rows) {
  第 161 行: definition.function: void insert_node(Node** head, int data) {
           内容: void insert_node(Node** head, int data) {
  第 169 行: definition.function: void print_list(Node* head) {
           内容: void print_list(Node* head) {
  第 178 行: definition.function: void free_list(Node* head) {
           内容: void free_list(Node* head) {
  第 188 行: definition.function: int main(int argc, char* argv[]) {
           内容: int main(int argc, char* argv[]) {

检测到的结构类型:
  - definition.enum: typedef enum {: 1 个
  - definition.function: inline int max(int a, int b) {: 1 个
  - definition.function: int add(int a, int b) {: 1 个
  - definition.function: int add(int a, int b);: 1 个
  - definition.function: int factorial(int n) {: 1 个
  - definition.function: int main(int argc, char* argv[]) {: 1 个
  - definition.function: int subtract(int a, int b) {: 1 个
  - definition.function: int sum_all(int count, ...) {: 1 个
  - definition.function: static int get_next_id(void) {: 1 个
  - definition.function: void free_list(Node* head) {: 1 个
  - definition.function: void free_point(Point* p) {: 1 个
  - definition.function: void free_point(Point* p);: 1 个
  - definition.function: void insert_node(Node** head, int data) {: 1 个
  - definition.function: void print_array(int arr[], int size) {: 1 个
  - definition.function: void print_array(int arr[], int size);: 1 个
  - definition.function: void print_list(Node* head) {: 1 个
  - definition.function: void process_matrix(int matrix[][3], int rows) {: 1 个
  - definition.function: void swap(int* a, int* b) {: 1 个
  - definition.struct: typedef struct Node {: 1 个
  - definition.struct: typedef struct {: 1 个
  - definition.union: typedef union {: 1 个

代码块信息 (6 个):
  块 1: 第 11-36 行 (26 行)
     11: #define MAX_SIZE 100
     12: #define PI 3.14159
     13: #define SQUARE(x) ((x) * (x))
    ... (还有 23 行)

  块 2: 第 39-64 行 (26 行)
     39: int global_counter = 0;
     40: static int static_global = 42;
     41: extern int external_var;
    ... (还有 23 行)

  块 3: 第 75-98 行 (24 行)
     75: void free_point(Point* p) {
     76:     if (p != NULL) {
     77:         free(p);
    ... (还有 21 行)

  块 4: 第 103-133 行 (31 行)
    103: int sum_all(int count, ...) {
    104:     va_list args;
    105:     va_start(args, count);
    ... (还有 28 行)

  块 5: 第 141-167 行 (27 行)
    141:     size_t len = strlen(src);
    142:     char* dest = (char*)malloc(len + 1);
    143:     
    ... (还有 24 行)

  块 6: 第 169-246 行 (78 行)
    169: void print_list(Node* head) {
    170:     Node* current = head;
    171:     while (current != NULL) {
    ... (还有 75 行)


统计信息:
  覆盖率: 85.8%
  块中总行数: 212
  结构类型数: 21
