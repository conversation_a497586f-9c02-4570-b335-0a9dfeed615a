FileParser 解析结果报告 - C
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c
  文件名: sample.c
  内容长度: 4685 字符
  行数: 247

关键结构行 (21 个):
  第  16 行: definition.struct: typedef struct {
           内容: typedef struct {
  第  21 行: definition.struct: typedef struct Node {
           内容: typedef struct Node {
  第  26 行: definition.enum: typedef enum {
           内容: typedef enum {
  第  32 行: definition.union: typedef union {
           内容: typedef union {
  第  44 行: name.definition.function: int add(int a, int b);
           内容: int add(int a, int b);
  第  45 行: name.definition.function: void print_array(int arr[], int size);
           内容: void print_array(int arr[], int size);
  第  47 行: name.definition.function: void free_point(Point* p);
           内容: void free_point(Point* p);
  第  50 行: name.definition.function: int add(int a, int b) {
           内容: int add(int a, int b) {
  第  54 行: name.definition.function: int subtract(int a, int b) {
           内容: int subtract(int a, int b) {
  第  58 行: name.definition.function: void print_array(int arr[], int size) {
           内容: void print_array(int arr[], int size) {
  第  75 行: name.definition.function: void free_point(Point* p) {
           内容: void free_point(Point* p) {
  第  82 行: name.definition.function: static int get_next_id(void) {
           内容: static int get_next_id(void) {
  第  88 行: name.definition.function: inline int max(int a, int b) {
           内容: inline int max(int a, int b) {
  第  93 行: name.definition.function: int factorial(int n) {
           内容: int factorial(int n) {
  第 103 行: name.definition.function: int sum_all(int count, ...) {
           内容: int sum_all(int count, ...) {
  第 117 行: name.definition.function: void swap(int* a, int* b) {
           内容: void swap(int* a, int* b) {
  第 127 行: name.definition.function: void process_matrix(int matrix[][3], int rows) {
           内容: void process_matrix(int matrix[][3], int rows) {
  第 161 行: name.definition.function: void insert_node(Node** head, int data) {
           内容: void insert_node(Node** head, int data) {
  第 169 行: name.definition.function: void print_list(Node* head) {
           内容: void print_list(Node* head) {
  第 178 行: name.definition.function: void free_list(Node* head) {
           内容: void free_list(Node* head) {
  第 188 行: name.definition.function: int main(int argc, char* argv[]) {
           内容: int main(int argc, char* argv[]) {

检测到的结构类型:
  - definition.enum: typedef enum {: 1 个
  - definition.struct: typedef struct Node {: 1 个
  - definition.struct: typedef struct {: 1 个
  - definition.union: typedef union {: 1 个
  - name.definition.function: inline int max(int a, int b) {: 1 个
  - name.definition.function: int add(int a, int b) {: 1 个
  - name.definition.function: int add(int a, int b);: 1 个
  - name.definition.function: int factorial(int n) {: 1 个
  - name.definition.function: int main(int argc, char* argv[]) {: 1 个
  - name.definition.function: int subtract(int a, int b) {: 1 个
  - name.definition.function: int sum_all(int count, ...) {: 1 个
  - name.definition.function: static int get_next_id(void) {: 1 个
  - name.definition.function: void free_list(Node* head) {: 1 个
  - name.definition.function: void free_point(Point* p) {: 1 个
  - name.definition.function: void free_point(Point* p);: 1 个
  - name.definition.function: void insert_node(Node** head, int data) {: 1 个
  - name.definition.function: void print_array(int arr[], int size) {: 1 个
  - name.definition.function: void print_array(int arr[], int size);: 1 个
  - name.definition.function: void print_list(Node* head) {: 1 个
  - name.definition.function: void process_matrix(int matrix[][3], int rows) {: 1 个
  - name.definition.function: void swap(int* a, int* b) {: 1 个

代码块信息 (18 个):
  块 1: 第 11-24 行 (14 行)
     11: #define MAX_SIZE 100
     12: #define PI 3.14159
     13: #define SQUARE(x) ((x) * (x))
    ... (还有 11 行)

  块 2: 第 26-36 行 (11 行)
     26: typedef enum {
     27:     RED,
     28:     GREEN,
    ... (还有 8 行)

  块 3: 第 39-52 行 (14 行)
     39: int global_counter = 0;
     40: static int static_global = 42;
     41: extern int external_var;
    ... (还有 11 行)

  块 4: 第 50-64 行 (15 行)
     50: int add(int a, int b) {
     51:     return a + b;
     52: }
    ... (还有 12 行)

  块 5: 第 58-79 行 (22 行)
     58: void print_array(int arr[], int size) {
     59:     printf("Array: ");
     60:     for (int i = 0; i < size; i++) {
    ... (还有 19 行)

  块 6: 第 75-85 行 (11 行)
     75: void free_point(Point* p) {
     76:     if (p != NULL) {
     77:         free(p);
    ... (还有 8 行)

  块 7: 第 82-98 行 (17 行)
     82: static int get_next_id(void) {
     83:     static int id = 0;
     84:     return ++id;
    ... (还有 14 行)

  块 8: 第 93-114 行 (22 行)
     93: int factorial(int n) {
     94:     if (n <= 1) {
     95:         return 1;
    ... (还有 19 行)

  块 9: 第 103-121 行 (19 行)
    103: int sum_all(int count, ...) {
    104:     va_list args;
    105:     va_start(args, count);
    ... (还有 16 行)

  块 10: 第 117-133 行 (17 行)
    117: void swap(int* a, int* b) {
    118:     int temp = *a;
    119:     *a = *b;
    ... (还有 14 行)

  块 11: 第 127-141 行 (15 行)
    127: void process_matrix(int matrix[][3], int rows) {
    128:     for (int i = 0; i < rows; i++) {
    129:         for (int j = 0; j < 3; j++) {
    ... (还有 12 行)

  块 12: 第 141-167 行 (27 行)
    141:     size_t len = strlen(src);
    142:     char* dest = (char*)malloc(len + 1);
    143:     
    ... (还有 24 行)

  块 13: 第 161-176 行 (16 行)
    161: void insert_node(Node** head, int data) {
    162:     Node* new_node = create_node(data);
    163:     if (new_node != NULL) {
    ... (还有 13 行)

  块 14: 第 169-185 行 (17 行)
    169: void print_list(Node* head) {
    170:     Node* current = head;
    171:     while (current != NULL) {
    ... (还有 14 行)

  块 15: 第 178-178 行 (1 行)
    178: void free_list(Node* head) {

  块 16: 第 188-198 行 (11 行)
    188: int main(int argc, char* argv[]) {
    189:     printf("C Sample Program\n");
    190:     
    ... (还有 8 行)

  块 17: 第 198-220 行 (23 行)
    198:     int result = add(10, 20);
    199:     printf("10 + 20 = %d\n", result);
    200:     
    ... (还有 20 行)

  块 18: 第 220-220 行 (1 行)
    220:     for (int i = 0; i < 5; i++) {


统计信息:
  覆盖率: 110.5%
  块中总行数: 273
  结构类型数: 21
