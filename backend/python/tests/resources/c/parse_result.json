{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c", "filename": "sample.c", "content_length": 4685, "line_count": 247}, "parsing_results": {"key_structure_lines": {"16": "definition.struct: typedef struct {", "21": "definition.struct: typedef struct Node {", "26": "definition.enum: typedef enum {", "32": "definition.union: typedef union {", "44": "definition.function: int add(int a, int b);", "45": "definition.function: void print_array(int arr[], int size);", "47": "definition.function: void free_point(Point* p);", "50": "definition.function: int add(int a, int b) {", "54": "definition.function: int subtract(int a, int b) {", "58": "definition.function: void print_array(int arr[], int size) {", "75": "definition.function: void free_point(Point* p) {", "82": "definition.function: static int get_next_id(void) {", "88": "definition.function: inline int max(int a, int b) {", "93": "definition.function: int factorial(int n) {", "103": "definition.function: int sum_all(int count, ...) {", "117": "definition.function: void swap(int* a, int* b) {", "127": "definition.function: void process_matrix(int matrix[][3], int rows) {", "161": "definition.function: void insert_node(Node** head, int data) {", "169": "definition.function: void print_list(Node* head) {", "178": "definition.function: void free_list(Node* head) {", "188": "definition.function: int main(int argc, char* argv[]) {"}, "key_structure_count": 21, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c", "start_line": 11, "end_line": 36, "content": "#define MAX_SIZE 100\n#define PI 3.14159\n#define SQUARE(x) ((x) * (x))\n\n// Type definitions\ntypedef struct {\n    int x;\n    int y;\n} Point;\n\ntypedef struct Node {\n    int data;\n    struct Node* next;\n} Node;\n\ntypedef enum {\n    RED,\n    GREEN,\n    BLUE\n} Color;\n\ntypedef union {\n    int i;\n    float f;\n    char c;\n} Value;", "line_count": 26}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c", "start_line": 39, "end_line": 64, "content": "int global_counter = 0;\nstatic int static_global = 42;\nextern int external_var;\n\n// Function prototypes\nint add(int a, int b);\nvoid print_array(int arr[], int size);\nPoint* create_point(int x, int y);\nvoid free_point(Point* p);\n\n// Function definitions\nint add(int a, int b) {\n    return a + b;\n}\n\nint subtract(int a, int b) {\n    return a - b;\n}\n\nvoid print_array(int arr[], int size) {\n    printf(\"Array: \");\n    for (int i = 0; i < size; i++) {\n        printf(\"%d \", arr[i]);\n    }\n    printf(\"\\n\");\n}", "line_count": 26}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c", "start_line": 75, "end_line": 98, "content": "void free_point(Point* p) {\n    if (p != NULL) {\n        free(p);\n    }\n}\n\n// Function with static storage class\nstatic int get_next_id(void) {\n    static int id = 0;\n    return ++id;\n}\n\n// Function with inline keyword\ninline int max(int a, int b) {\n    return (a > b) ? a : b;\n}\n\n// Recursive function\nint factorial(int n) {\n    if (n <= 1) {\n        return 1;\n    }\n    return n * factorial(n - 1);\n}", "line_count": 24}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c", "start_line": 103, "end_line": 133, "content": "int sum_all(int count, ...) {\n    va_list args;\n    va_start(args, count);\n    \n    int sum = 0;\n    for (int i = 0; i < count; i++) {\n        sum += va_arg(args, int);\n    }\n    \n    va_end(args);\n    return sum;\n}\n\n// Pointer functions\nvoid swap(int* a, int* b) {\n    int temp = *a;\n    *a = *b;\n    *b = temp;\n}\n\n// Function pointer\nint (*operation)(int, int) = add;\n\n// Array processing function\nvoid process_matrix(int matrix[][3], int rows) {\n    for (int i = 0; i < rows; i++) {\n        for (int j = 0; j < 3; j++) {\n            matrix[i][j] *= 2;\n        }\n    }\n}", "line_count": 31}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c", "start_line": 141, "end_line": 167, "content": "    size_t len = strlen(src);\n    char* dest = (char*)malloc(len + 1);\n    \n    if (dest != NULL) {\n        strcpy(dest, src);\n    }\n    \n    return dest;\n}\n\n// Linked list operations\nNode* create_node(int data) {\n    Node* node = (Node*)malloc(sizeof(Node));\n    if (node != NULL) {\n        node->data = data;\n        node->next = NULL;\n    }\n    return node;\n}\n\nvoid insert_node(Node** head, int data) {\n    Node* new_node = create_node(data);\n    if (new_node != NULL) {\n        new_node->next = *head;\n        *head = new_node;\n    }\n}", "line_count": 27}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c", "start_line": 169, "end_line": 246, "content": "void print_list(Node* head) {\n    Node* current = head;\n    while (current != NULL) {\n        printf(\"%d -> \", current->data);\n        current = current->next;\n    }\n    printf(\"NULL\\n\");\n}\n\nvoid free_list(Node* head) {\n    Node* current = head;\n    while (current != NULL) {\n        Node* next = current->next;\n        free(current);\n        current = next;\n    }\n}\n\n// Main function\nint main(int argc, char* argv[]) {\n    printf(\"C Sample Program\\n\");\n    \n    // Variable declarations\n    int numbers[] = {1, 2, 3, 4, 5};\n    int size = sizeof(numbers) / sizeof(numbers[0]);\n    \n    // Function calls\n    print_array(numbers, size);\n    \n    int result = add(10, 20);\n    printf(\"10 + 20 = %d\\n\", result);\n    \n    // Pointer usage\n    Point* p1 = create_point(3, 4);\n    if (p1 != NULL) {\n        printf(\"Point: (%d, %d)\\n\", p1->x, p1->y);\n        free_point(p1);\n    }\n    \n    // Linked list example\n    Node* list = NULL;\n    insert_node(&list, 1);\n    insert_node(&list, 2);\n    insert_node(&list, 3);\n    \n    printf(\"Linked list: \");\n    print_list(list);\n    \n    free_list(list);\n    \n    // Control structures\n    for (int i = 0; i < 5; i++) {\n        if (i % 2 == 0) {\n            printf(\"%d is even\\n\", i);\n        } else {\n            printf(\"%d is odd\\n\", i);\n        }\n    }\n    \n    // Switch statement\n    Color color = RED;\n    switch (color) {\n        case RED:\n            printf(\"Color is red\\n\");\n            break;\n        case GREEN:\n            printf(\"Color is green\\n\");\n            break;\n        case BLUE:\n            printf(\"Color is blue\\n\");\n            break;\n        default:\n            printf(\"Unknown color\\n\");\n            break;\n    }\n    \n    return 0;\n}", "line_count": 78}], "chunk_count": 6}, "analysis": {"detected_structures": ["definition.function: int subtract(int a, int b) {", "definition.function: int add(int a, int b);", "definition.function: void print_array(int arr[], int size);", "definition.function: void print_list(Node* head) {", "definition.function: int sum_all(int count, ...) {", "definition.function: void insert_node(Node** head, int data) {", "definition.function: static int get_next_id(void) {", "definition.function: int add(int a, int b) {", "definition.union: typedef union {", "definition.function: void print_array(int arr[], int size) {", "definition.function: int factorial(int n) {", "definition.function: void free_point(Point* p);", "definition.function: inline int max(int a, int b) {", "definition.function: void free_list(Node* head) {", "definition.function: int main(int argc, char* argv[]) {", "definition.enum: typedef enum {", "definition.function: void swap(int* a, int* b) {", "definition.function: void process_matrix(int matrix[][3], int rows) {", "definition.struct: typedef struct {", "definition.function: void free_point(Point* p) {", "definition.struct: typedef struct Node {"], "structure_types_count": 21, "total_lines_in_chunks": 212, "coverage_percentage": 85.82995951417004}}