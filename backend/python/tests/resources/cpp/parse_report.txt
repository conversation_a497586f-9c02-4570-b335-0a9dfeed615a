FileParser 解析结果报告 - CPP
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp
  文件名: sample.cpp
  内容长度: 7645 字符
  行数: 282

关键结构行 (4 个):
  第  16 行: name.definition.class: class Point {
           内容: class Point {
  第  93 行: name.definition.class: class Shape {
           内容: class Shape {
  第 115 行: name.definition.class: class Circle : public Shape {
           内容: class Circle : public Shape {
  第 147 行: name.definition.class: class Container {
           内容: class Container {

检测到的结构类型:
  - name.definition.class: class Circle : public Shape {: 1 个
  - name.definition.class: class Container {: 1 个
  - name.definition.class: class Point {: 1 个
  - name.definition.class: class Shape {: 1 个

代码块信息 (25 个):
  块 1: 第 13-13 行 (1 行)
     13: namespace geometry {

  块 2: 第 16-27 行 (12 行)
     16:     class Point {
     17:     private:
     18:         double x_, y_;
    ... (还有 9 行)

  块 3: 第 27-42 行 (16 行)
     27:         ~Point() = default;
     28:         
     29:         // Assignment operator
    ... (还有 13 行)

  块 4: 第 39-56 行 (18 行)
     39:         Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {
     40:             other.x_ = 0.0;
     41:             other.y_ = 0.0;
    ... (还有 15 行)

  块 5: 第 56-66 行 (11 行)
     56:         double getX() const { return x_; }
     57:         double getY() const { return y_; }
     58:         
    ... (还有 8 行)

  块 6: 第 64-76 行 (13 行)
     64:         Point operator+(const Point& other) const {
     65:             return Point(x_ + other.x_, y_ + other.y_);
     66:         }
    ... (还有 10 行)

  块 7: 第 74-89 行 (16 行)
     74:         bool operator==(const Point& other) const {
     75:             return x_ == other.x_ && y_ == other.y_;
     76:         }
    ... (还有 13 行)

  块 8: 第 85-112 行 (28 行)
     85:         static double distance(const Point& p1, const Point& p2) {
     86:             double dx = p1.x_ - p2.x_;
     87:             double dy = p1.y_ - p2.y_;
    ... (还有 25 行)

  块 9: 第 93-108 行 (16 行)
     93:     class Shape {
     94:     protected:
     95:         std::string color_;
    ... (还有 13 行)

  块 10: 第 106-141 行 (36 行)
    106:         virtual void draw() const {
    107:             std::cout << "Drawing a " << color_ << " shape" << std::endl;
    108:         }
    ... (还有 33 行)

  块 11: 第 115-127 行 (13 行)
    115:     class Circle : public Shape {
    116:     private:
    117:         Point center_;
    ... (还有 10 行)

  块 12: 第 125-136 行 (12 行)
    125:         double area() const override {
    126:             return 3.14159 * radius_ * radius_;
    127:         }
    ... (还有 9 行)

  块 13: 第 133-181 行 (49 行)
    133:         void draw() const override {
    134:             std::cout << "Drawing a " << color_ << " circle at " << center_ 
    135:                       << " with radius " << radius_ << std::endl;
    ... (还有 46 行)

  块 14: 第 147-160 行 (14 行)
    147: class Container {
    148: private:
    149:     std::vector<T> data_;
    ... (还有 11 行)

  块 15: 第 158-169 行 (12 行)
    158:     void add(U&& item) {
    159:         data_.emplace_back(std::forward<U>(item));
    160:     }
    ... (还有 9 行)

  块 16: 第 169-180 行 (12 行)
    169:     size_t size() const { return data_.size(); }
    170:     T& operator[](size_t index) { return data_[index]; }
    171:     const T& operator[](size_t index) const { return data_[index]; }
    ... (还有 9 行)

  块 17: 第 175-192 行 (18 行)
    175:     auto filter(Predicate pred) const {
    176:         Container<T> result;
    177:         std::copy_if(data_.begin(), data_.end(), 
    ... (还有 15 行)

  块 18: 第 190-203 行 (14 行)
    190:     void add(bool value) {
    191:         data_.push_back(value);
    192:     }
    ... (还有 11 行)

  块 19: 第 201-215 行 (15 行)
    201: T max(const T& a, const T& b) {
    202:     return (a > b) ? a : b;
    203: }
    ... (还有 12 行)

  块 20: 第 212-254 行 (43 行)
    212: void print(Args... args) {
    213:     ((std::cout << args << " "), ...);
    214:     std::cout << std::endl;
    ... (还有 40 行)

  块 21: 第 218-229 行 (12 行)
    218: void modernCppFeatures() {
    219:     // Lambda expressions
    220:     auto lambda1 = [](int x) { return x * x; };
    ... (还有 9 行)

  块 22: 第 229-243 行 (15 行)
    229:     auto generic_lambda = [](auto x, auto y) { return x + y; };
    230:     
    231:     // Smart pointers
    ... (还有 12 行)

  块 23: 第 243-281 行 (39 行)
    243:     auto result = add(3.14, 2);
    244:     
    245:     // Structured bindings (C++17)
    ... (还有 36 行)

  块 24: 第 257-269 行 (13 行)
    257: int main() {
    258:     using namespace geometry;
    259:     
    ... (还有 10 行)

  块 25: 第 269-269 行 (1 行)
    269:     std::unique_ptr<Shape> shape = std::make_unique<Circle>(p1, 5.0, "blue");


统计信息:
  覆盖率: 159.2%
  块中总行数: 449
  结构类型数: 4
