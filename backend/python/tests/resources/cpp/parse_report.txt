FileParser 解析结果报告 - CPP
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp
  文件名: sample.cpp
  内容长度: 7645 字符
  行数: 282

关键结构行 (43 个):
  第  13 行: definition.namespace: namespace geometry {
           内容: namespace geometry {
  第  16 行: definition.class: class Point {
           内容: class Point {
  第  22 行: definition.constructor: Point() : x_(0.0), y_(0.0) {}
           内容: Point() : x_(0.0), y_(0.0) {}
  第  23 行: definition.constructor: Point(double x, double y) : x_(x), y_(y) {}
           内容: Point(double x, double y) : x_(x), y_(y) {}
  第  24 行: definition.constructor: Point(const Point& other) : x_(other.x_), y_(other.y_) {}
           内容: Point(const Point& other) : x_(other.x_), y_(other.y_) {}
  第  27 行: definition.destructor: ~Point() = default;
           内容: ~Point() = default;
  第  39 行: definition.constructor: Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {
           内容: Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {
  第  56 行: definition.method: double getX() const { return x_; }
           内容: double getX() const { return x_; }
  第  57 行: definition.method: double getY() const { return y_; }
           内容: double getY() const { return y_; }
  第  60 行: definition.method: void setX(double x) { x_ = x; }
           内容: void setX(double x) { x_ = x; }
  第  61 行: definition.method: void setY(double y) { y_ = y; }
           内容: void setY(double y) { y_ = y; }
  第  64 行: definition.operator: Point operator+(const Point& other) const {
           内容: Point operator+(const Point& other) const {
  第  74 行: definition.operator: bool operator==(const Point& other) const {
           内容: bool operator==(const Point& other) const {
  第  85 行: definition.method: static double distance(const Point& p1, const Point& p2) {
           内容: static double distance(const Point& p1, const Point& p2) {
  第  93 行: definition.class: class Shape {
           内容: class Shape {
  第  98 行: definition.constructor: Shape(const std::string& color) : color_(color) {}
           内容: Shape(const std::string& color) : color_(color) {}
  第  99 行: definition.destructor: virtual ~Shape() = default;
           内容: virtual ~Shape() = default;
  第 106 行: definition.method: virtual void draw() const {
           内容: virtual void draw() const {
  第 115 行: definition.class: class Circle : public Shape {
           内容: class Circle : public Shape {
  第 121 行: definition.constructor: Circle(const Point& center, double radius, const std::string& color)
           内容: Circle(const Point& center, double radius, const std::string& color)
  第 125 行: definition.method: double area() const override {
           内容: double area() const override {
  第 129 行: definition.method: double perimeter() const override {
           内容: double perimeter() const override {
  第 133 行: definition.method: void draw() const override {
           内容: void draw() const override {
  第 140 行: definition.method: double getRadius() const { return radius_; }
           内容: double getRadius() const { return radius_; }
  第 146 行: definition.template: template<typename T>
           内容: template<typename T>
  第 147 行: definition.class: class Container {
           内容: class Container {
  第 153 行: definition.constructor: Container() = default;
           内容: Container() = default;
  第 154 行: definition.constructor: Container(std::initializer_list<T> init) : data_(init) {}
           内容: Container(std::initializer_list<T> init) : data_(init) {}
  第 158 行: definition.constructor: void add(U&& item) {
           内容: void add(U&& item) {
  第 163 行: definition.method: auto begin() { return data_.begin(); }
           内容: auto begin() { return data_.begin(); }
  第 164 行: definition.method: auto end() { return data_.end(); }
           内容: auto end() { return data_.end(); }
  第 165 行: definition.method: auto begin() const { return data_.begin(); }
           内容: auto begin() const { return data_.begin(); }
  第 166 行: definition.method: auto end() const { return data_.end(); }
           内容: auto end() const { return data_.end(); }
  第 169 行: definition.method: size_t size() const { return data_.size(); }
           内容: size_t size() const { return data_.size(); }
  第 175 行: definition.constructor: auto filter(Predicate pred) const {
           内容: auto filter(Predicate pred) const {
  第 190 行: definition.method: void add(bool value) {
           内容: void add(bool value) {
  第 194 行: definition.method: size_t size() const { return data_.size(); }
           内容: size_t size() const { return data_.size(); }
  第 196 行: definition.operator: bool operator[](size_t index) const { return data_[index]; }
           内容: bool operator[](size_t index) const { return data_[index]; }
  第 201 行: definition.constructor: T max(const T& a, const T& b) {
           内容: T max(const T& a, const T& b) {
  第 206 行: definition.constructor: auto add(const T& a, const U& b) -> decltype(a + b) {
           内容: auto add(const T& a, const U& b) -> decltype(a + b) {
  第 212 行: definition.constructor: void print(Args... args) {
           内容: void print(Args... args) {
  第 218 行: definition.constructor: void modernCppFeatures() {
           内容: void modernCppFeatures() {
  第 257 行: definition.constructor: int main() {
           内容: int main() {

检测到的结构类型:
  - definition.class: class Circle : public Shape {: 1 个
  - definition.class: class Container {: 1 个
  - definition.class: class Point {: 1 个
  - definition.class: class Shape {: 1 个
  - definition.constructor: Circle(const Point& center, double radius, const std::string& color): 1 个
  - definition.constructor: Container() = default;: 1 个
  - definition.constructor: Container(std::initializer_list<T> init) : data_(init) {}: 1 个
  - definition.constructor: Point() : x_(0.0), y_(0.0) {}: 1 个
  - definition.constructor: Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {: 1 个
  - definition.constructor: Point(const Point& other) : x_(other.x_), y_(other.y_) {}: 1 个
  - definition.constructor: Point(double x, double y) : x_(x), y_(y) {}: 1 个
  - definition.constructor: Shape(const std::string& color) : color_(color) {}: 1 个
  - definition.constructor: T max(const T& a, const T& b) {: 1 个
  - definition.constructor: auto add(const T& a, const U& b) -> decltype(a + b) {: 1 个
  - definition.constructor: auto filter(Predicate pred) const {: 1 个
  - definition.constructor: int main() {: 1 个
  - definition.constructor: void add(U&& item) {: 1 个
  - definition.constructor: void modernCppFeatures() {: 1 个
  - definition.constructor: void print(Args... args) {: 1 个
  - definition.destructor: virtual ~Shape() = default;: 1 个
  - definition.destructor: ~Point() = default;: 1 个
  - definition.method: auto begin() const { return data_.begin(); }: 1 个
  - definition.method: auto begin() { return data_.begin(); }: 1 个
  - definition.method: auto end() const { return data_.end(); }: 1 个
  - definition.method: auto end() { return data_.end(); }: 1 个
  - definition.method: double area() const override {: 1 个
  - definition.method: double getRadius() const { return radius_; }: 1 个
  - definition.method: double getX() const { return x_; }: 1 个
  - definition.method: double getY() const { return y_; }: 1 个
  - definition.method: double perimeter() const override {: 1 个
  - definition.method: size_t size() const { return data_.size(); }: 2 个
  - definition.method: static double distance(const Point& p1, const Point& p2) {: 1 个
  - definition.method: virtual void draw() const {: 1 个
  - definition.method: void add(bool value) {: 1 个
  - definition.method: void draw() const override {: 1 个
  - definition.method: void setX(double x) { x_ = x; }: 1 个
  - definition.method: void setY(double y) { y_ = y; }: 1 个
  - definition.namespace: namespace geometry {: 1 个
  - definition.operator: Point operator+(const Point& other) const {: 1 个
  - definition.operator: bool operator==(const Point& other) const {: 1 个
  - definition.operator: bool operator[](size_t index) const { return data_[index]; }: 1 个
  - definition.template: template<typename T>: 1 个

代码块信息 (7 个):
  块 1: 第 13-143 行 (131 行)
     13: namespace geometry {
     14:     
     15:     // Class declaration
    ... (还有 128 行)

  块 2: 第 13-113 行 (101 行)
     13: namespace geometry {
     14:     
     15:     // Class declaration
    ... (还有 100 行)

  块 3: 第 113-143 行 (31 行)
    113: namespace geometry {
    114: ...
    115:     
    ... (还有 30 行)

  块 4: 第 146-181 行 (36 行)
    146: template<typename T>
    147: class Container {
    148: private:
    ... (还有 33 行)

  块 5: 第 190-215 行 (26 行)
    190:     void add(bool value) {
    191:         data_.push_back(value);
    192:     }
    ... (还有 23 行)

  块 6: 第 218-254 行 (37 行)
    218: void modernCppFeatures() {
    219:     // Lambda expressions
    220:     auto lambda1 = [](int x) { return x * x; };
    ... (还有 34 行)

  块 7: 第 257-281 行 (25 行)
    257: int main() {
    258:     using namespace geometry;
    259:     
    ... (还有 22 行)


统计信息:
  覆盖率: 137.2%
  块中总行数: 387
  结构类型数: 42
