{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "filename": "sample.cpp", "content_length": 7645, "line_count": 282}, "parsing_results": {"key_structure_lines": {"16": "name.definition.class: class Point {", "93": "name.definition.class: class Shape {", "115": "name.definition.class: class Circle : public Shape {", "147": "name.definition.class: class Container {"}, "key_structure_count": 4, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 13, "end_line": 13, "content": "namespace geometry {", "line_count": 1}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 16, "end_line": 27, "content": "    class Point {\n    private:\n        double x_, y_;\n        \n    public:\n        // Constructors\n        Point() : x_(0.0), y_(0.0) {}\n        Point(double x, double y) : x_(x), y_(y) {}\n        Point(const Point& other) : x_(other.x_), y_(other.y_) {}\n        \n        // Destructor\n        ~Point() = default;", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 27, "end_line": 42, "content": "        ~Point() = default;\n        \n        // Assignment operator\n        Point& operator=(const Point& other) {\n            if (this != &other) {\n                x_ = other.x_;\n                y_ = other.y_;\n            }\n            return *this;\n        }\n        \n        // Move constructor\n        Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {\n            other.x_ = 0.0;\n            other.y_ = 0.0;\n        }", "line_count": 16}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 39, "end_line": 56, "content": "        Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {\n            other.x_ = 0.0;\n            other.y_ = 0.0;\n        }\n        \n        // Move assignment operator\n        Point& operator=(Point&& other) noexcept {\n            if (this != &other) {\n                x_ = other.x_;\n                y_ = other.y_;\n                other.x_ = 0.0;\n                other.y_ = 0.0;\n            }\n            return *this;\n        }\n        \n        // Getters\n        double getX() const { return x_; }", "line_count": 18}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 56, "end_line": 66, "content": "        double getX() const { return x_; }\n        double getY() const { return y_; }\n        \n        // Setters\n        void setX(double x) { x_ = x; }\n        void setY(double y) { y_ = y; }\n        \n        // Operator overloading\n        Point operator+(const Point& other) const {\n            return Point(x_ + other.x_, y_ + other.y_);\n        }", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 64, "end_line": 76, "content": "        Point operator+(const Point& other) const {\n            return Point(x_ + other.x_, y_ + other.y_);\n        }\n        \n        Point& operator+=(const Point& other) {\n            x_ += other.x_;\n            y_ += other.y_;\n            return *this;\n        }\n        \n        bool operator==(const Point& other) const {\n            return x_ == other.x_ && y_ == other.y_;\n        }", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 74, "end_line": 89, "content": "        bool operator==(const Point& other) const {\n            return x_ == other.x_ && y_ == other.y_;\n        }\n        \n        // Friend function\n        friend std::ostream& operator<<(std::ostream& os, const Point& p) {\n            os << \"(\" << p.x_ << \", \" << p.y_ << \")\";\n            return os;\n        }\n        \n        // Static method\n        static double distance(const Point& p1, const Point& p2) {\n            double dx = p1.x_ - p2.x_;\n            double dy = p1.y_ - p2.y_;\n            return std::sqrt(dx * dx + dy * dy);\n        }", "line_count": 16}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 85, "end_line": 112, "content": "        static double distance(const Point& p1, const Point& p2) {\n            double dx = p1.x_ - p2.x_;\n            double dy = p1.y_ - p2.y_;\n            return std::sqrt(dx * dx + dy * dy);\n        }\n    };\n    \n    // Abstract base class\n    class Shape {\n    protected:\n        std::string color_;\n        \n    public:\n        Shape(const std::string& color) : color_(color) {}\n        virtual ~Shape() = default;\n        \n        // Pure virtual functions\n        virtual double area() const = 0;\n        virtual double perimeter() const = 0;\n        \n        // Virtual function\n        virtual void draw() const {\n            std::cout << \"Drawing a \" << color_ << \" shape\" << std::endl;\n        }\n        \n        // Getter\n        const std::string& getColor() const { return color_; }\n    };", "line_count": 28}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 93, "end_line": 108, "content": "    class Shape {\n    protected:\n        std::string color_;\n        \n    public:\n        Shape(const std::string& color) : color_(color) {}\n        virtual ~Shape() = default;\n        \n        // Pure virtual functions\n        virtual double area() const = 0;\n        virtual double perimeter() const = 0;\n        \n        // Virtual function\n        virtual void draw() const {\n            std::cout << \"Drawing a \" << color_ << \" shape\" << std::endl;\n        }", "line_count": 16}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 106, "end_line": 141, "content": "        virtual void draw() const {\n            std::cout << \"Drawing a \" << color_ << \" shape\" << std::endl;\n        }\n        \n        // Getter\n        const std::string& getColor() const { return color_; }\n    };\n    \n    // Derived class\n    class Circle : public Shape {\n    private:\n        Point center_;\n        double radius_;\n        \n    public:\n        Circle(const Point& center, double radius, const std::string& color)\n            : Shape(color), center_(center), radius_(radius) {}\n        \n        // Override virtual functions\n        double area() const override {\n            return 3.14159 * radius_ * radius_;\n        }\n        \n        double perimeter() const override {\n            return 2 * 3.14159 * radius_;\n        }\n        \n        void draw() const override {\n            std::cout << \"Drawing a \" << color_ << \" circle at \" << center_ \n                      << \" with radius \" << radius_ << std::endl;\n        }\n        \n        // Getters\n        const Point& getCenter() const { return center_; }\n        double getRadius() const { return radius_; }\n    };", "line_count": 36}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 115, "end_line": 127, "content": "    class Circle : public Shape {\n    private:\n        Point center_;\n        double radius_;\n        \n    public:\n        Circle(const Point& center, double radius, const std::string& color)\n            : Shape(color), center_(center), radius_(radius) {}\n        \n        // Override virtual functions\n        double area() const override {\n            return 3.14159 * radius_ * radius_;\n        }", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 125, "end_line": 136, "content": "        double area() const override {\n            return 3.14159 * radius_ * radius_;\n        }\n        \n        double perimeter() const override {\n            return 2 * 3.14159 * radius_;\n        }\n        \n        void draw() const override {\n            std::cout << \"Drawing a \" << color_ << \" circle at \" << center_ \n                      << \" with radius \" << radius_ << std::endl;\n        }", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 133, "end_line": 181, "content": "        void draw() const override {\n            std::cout << \"Drawing a \" << color_ << \" circle at \" << center_ \n                      << \" with radius \" << radius_ << std::endl;\n        }\n        \n        // Getters\n        const Point& getCenter() const { return center_; }\n        double getRadius() const { return radius_; }\n    };\n    \n} // namespace geometry\n\n// Template class\ntemplate<typename T>\nclass Container {\nprivate:\n    std::vector<T> data_;\n    \npublic:\n    // Constructor\n    Container() = default;\n    Container(std::initializer_list<T> init) : data_(init) {}\n    \n    // Template member function\n    template<typename U>\n    void add(U&& item) {\n        data_.emplace_back(std::forward<U>(item));\n    }\n    \n    // Iterator support\n    auto begin() { return data_.begin(); }\n    auto end() { return data_.end(); }\n    auto begin() const { return data_.begin(); }\n    auto end() const { return data_.end(); }\n    \n    // Size and access\n    size_t size() const { return data_.size(); }\n    T& operator[](size_t index) { return data_[index]; }\n    const T& operator[](size_t index) const { return data_[index]; }\n    \n    // Template method with constraints (C++20)\n    template<typename Predicate>\n    auto filter(Predicate pred) const {\n        Container<T> result;\n        std::copy_if(data_.begin(), data_.end(), \n                     std::back_inserter(result.data_), pred);\n        return result;\n    }\n};", "line_count": 49}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 147, "end_line": 160, "content": "class Container {\nprivate:\n    std::vector<T> data_;\n    \npublic:\n    // Constructor\n    Container() = default;\n    Container(std::initializer_list<T> init) : data_(init) {}\n    \n    // Template member function\n    template<typename U>\n    void add(U&& item) {\n        data_.emplace_back(std::forward<U>(item));\n    }", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 158, "end_line": 169, "content": "    void add(U&& item) {\n        data_.emplace_back(std::forward<U>(item));\n    }\n    \n    // Iterator support\n    auto begin() { return data_.begin(); }\n    auto end() { return data_.end(); }\n    auto begin() const { return data_.begin(); }\n    auto end() const { return data_.end(); }\n    \n    // Size and access\n    size_t size() const { return data_.size(); }", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 169, "end_line": 180, "content": "    size_t size() const { return data_.size(); }\n    T& operator[](size_t index) { return data_[index]; }\n    const T& operator[](size_t index) const { return data_[index]; }\n    \n    // Template method with constraints (C++20)\n    template<typename Predicate>\n    auto filter(Predicate pred) const {\n        Container<T> result;\n        std::copy_if(data_.begin(), data_.end(), \n                     std::back_inserter(result.data_), pred);\n        return result;\n    }", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 175, "end_line": 192, "content": "    auto filter(Predicate pred) const {\n        Container<T> result;\n        std::copy_if(data_.begin(), data_.end(), \n                     std::back_inserter(result.data_), pred);\n        return result;\n    }\n};\n\n// Template specialization\ntemplate<>\nclass Container<bool> {\nprivate:\n    std::vector<bool> data_;\n    \npublic:\n    void add(bool value) {\n        data_.push_back(value);\n    }", "line_count": 18}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 190, "end_line": 203, "content": "    void add(bool value) {\n        data_.push_back(value);\n    }\n    \n    size_t size() const { return data_.size(); }\n    \n    bool operator[](size_t index) const { return data_[index]; }\n};\n\n// Function templates\ntemplate<typename T>\nT max(const T& a, const T& b) {\n    return (a > b) ? a : b;\n}", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 201, "end_line": 215, "content": "T max(const T& a, const T& b) {\n    return (a > b) ? a : b;\n}\n\ntemplate<typename T, typename U>\nauto add(const T& a, const U& b) -> decltype(a + b) {\n    return a + b;\n}\n\n// Variadic template\ntemplate<typename... Args>\nvoid print(Args... args) {\n    ((std::cout << args << \" \"), ...);\n    std::cout << std::endl;\n}", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 212, "end_line": 254, "content": "void print(Args... args) {\n    ((std::cout << args << \" \"), ...);\n    std::cout << std::endl;\n}\n\n// Lambda expressions and modern C++ features\nvoid modernCppFeatures() {\n    // Lambda expressions\n    auto lambda1 = [](int x) { return x * x; };\n    auto lambda2 = [](const auto& a, const auto& b) { return a + b; };\n    \n    // Capture by value and reference\n    int multiplier = 10;\n    auto lambda3 = [multiplier](int x) { return x * multiplier; };\n    auto lambda4 = [&multiplier](int x) { multiplier += x; return multiplier; };\n    \n    // Generic lambda (C++14)\n    auto generic_lambda = [](auto x, auto y) { return x + y; };\n    \n    // Smart pointers\n    auto unique_ptr = std::make_unique<geometry::Point>(1.0, 2.0);\n    auto shared_ptr = std::make_shared<geometry::Circle>(*unique_ptr, 5.0, \"red\");\n    \n    // Range-based for loop\n    std::vector<int> numbers = {1, 2, 3, 4, 5};\n    for (const auto& num : numbers) {\n        std::cout << num << \" \";\n    }\n    std::cout << std::endl;\n    \n    // Auto type deduction\n    auto result = add(3.14, 2);\n    \n    // Structured bindings (C++17)\n    auto [x, y] = std::make_pair(10, 20);\n    \n    // std::function\n    std::function<int(int, int)> operation = [](int a, int b) { return a + b; };\n    \n    // Algorithm usage\n    std::transform(numbers.begin(), numbers.end(), numbers.begin(),\n                   [](int x) { return x * 2; });\n}", "line_count": 43}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 218, "end_line": 229, "content": "void modernCppFeatures() {\n    // Lambda expressions\n    auto lambda1 = [](int x) { return x * x; };\n    auto lambda2 = [](const auto& a, const auto& b) { return a + b; };\n    \n    // Capture by value and reference\n    int multiplier = 10;\n    auto lambda3 = [multiplier](int x) { return x * multiplier; };\n    auto lambda4 = [&multiplier](int x) { multiplier += x; return multiplier; };\n    \n    // Generic lambda (C++14)\n    auto generic_lambda = [](auto x, auto y) { return x + y; };", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 229, "end_line": 243, "content": "    auto generic_lambda = [](auto x, auto y) { return x + y; };\n    \n    // Smart pointers\n    auto unique_ptr = std::make_unique<geometry::Point>(1.0, 2.0);\n    auto shared_ptr = std::make_shared<geometry::Circle>(*unique_ptr, 5.0, \"red\");\n    \n    // Range-based for loop\n    std::vector<int> numbers = {1, 2, 3, 4, 5};\n    for (const auto& num : numbers) {\n        std::cout << num << \" \";\n    }\n    std::cout << std::endl;\n    \n    // Auto type deduction\n    auto result = add(3.14, 2);", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 243, "end_line": 281, "content": "    auto result = add(3.14, 2);\n    \n    // Structured bindings (C++17)\n    auto [x, y] = std::make_pair(10, 20);\n    \n    // std::function\n    std::function<int(int, int)> operation = [](int a, int b) { return a + b; };\n    \n    // Algorithm usage\n    std::transform(numbers.begin(), numbers.end(), numbers.begin(),\n                   [](int x) { return x * 2; });\n}\n\n// Main function\nint main() {\n    using namespace geometry;\n    \n    // Object creation\n    Point p1(1.0, 2.0);\n    Point p2(3.0, 4.0);\n    \n    // Method calls\n    Point p3 = p1 + p2;\n    std::cout << \"p1 + p2 = \" << p3 << std::endl;\n    \n    // Polymorphism\n    std::unique_ptr<Shape> shape = std::make_unique<Circle>(p1, 5.0, \"blue\");\n    shape->draw();\n    std::cout << \"Area: \" << shape->area() << std::endl;\n    \n    // Template usage\n    Container<int> int_container{1, 2, 3, 4, 5};\n    Container<std::string> string_container{\"hello\", \"world\"};\n    \n    // Modern C++ features\n    modernCppFeatures();\n    \n    return 0;\n}", "line_count": 39}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 257, "end_line": 269, "content": "int main() {\n    using namespace geometry;\n    \n    // Object creation\n    Point p1(1.0, 2.0);\n    Point p2(3.0, 4.0);\n    \n    // Method calls\n    Point p3 = p1 + p2;\n    std::cout << \"p1 + p2 = \" << p3 << std::endl;\n    \n    // Polymorphism\n    std::unique_ptr<Shape> shape = std::make_unique<Circle>(p1, 5.0, \"blue\");", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 269, "end_line": 269, "content": "    std::unique_ptr<Shape> shape = std::make_unique<Circle>(p1, 5.0, \"blue\");", "line_count": 1}], "chunk_count": 25}, "analysis": {"detected_structures": ["name.definition.class: class Container {", "name.definition.class: class Point {", "name.definition.class: class Circle : public Shape {", "name.definition.class: class Shape {"], "structure_types_count": 4, "total_lines_in_chunks": 449, "coverage_percentage": 159.21985815602838}}