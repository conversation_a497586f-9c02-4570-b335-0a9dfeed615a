FileParser 解析结果报告 - CSS
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css
  文件名: sample.css
  内容长度: 6499 字符
  行数: 378

关键结构行 (0 个):
  无检测到关键结构

检测到的结构类型:
  无结构类型检测到

代码块信息 (16 个):
  块 1: 第 5-25 行 (21 行)
      5:     --primary-color: #3498db;
      6:     --secondary-color: #2ecc71;
      7:     --accent-color: #e74c3c;
    ... (还有 18 行)

  块 2: 第 26-46 行 (21 行)
     26:     color: var(--text-color);
     27:     background-color: var(--background-color);
     28: }
    ... (还有 18 行)

  块 3: 第 47-67 行 (21 行)
     47:     text-decoration: underline;
     48: }
     49: 
    ... (还有 18 行)

  块 4: 第 68-90 行 (23 行)
     68:     border-radius: var(--border-radius);
     69:     cursor: pointer;
     70:     transition: var(--transition);
    ... (还有 20 行)

  块 5: 第 91-111 行 (21 行)
     91:     color: white;
     92:     padding: 1rem 0;
     93: }
    ... (还有 18 行)

  块 6: 第 112-134 行 (23 行)
    112:     padding: 12px;
    113:     border: 1px solid #ddd;
    114:     border-radius: var(--border-radius);
    ... (还有 20 行)

  块 7: 第 138-162 行 (25 行)
    138:     margin-right: 0;
    139: }
    140: 
    ... (还有 22 行)

  块 8: 第 163-185 行 (23 行)
    163:     color: var(--primary-color);
    164: }
    165: 
    ... (还有 20 行)

  块 9: 第 186-207 行 (22 行)
    186:     padding-top: 2rem;
    187: }
    188: 
    ... (还有 19 行)

  块 10: 第 212-232 行 (21 行)
    212:     display: grid;
    213:     grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    214:     grid-gap: 2rem;
    ... (还有 18 行)

  块 11: 第 233-257 行 (25 行)
    233:         transform: translateY(0);
    234:     }
    235: }
    ... (还有 22 行)

  块 12: 第 261-283 行 (23 行)
    261:         flex-direction: column;
    262:     }
    263:     
    ... (还有 20 行)

  块 13: 第 289-310 行 (22 行)
    289:         display: none;
    290:     }
    291:     
    ... (还有 19 行)

  块 14: 第 314-337 行 (24 行)
    314:     background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(52,152,219,1) 100%);
    315: }
    316: 
    ... (还有 21 行)

  块 15: 第 338-359 行 (22 行)
    338:     border-radius: var(--border-radius);
    339:     
    340:     &:hover {
    ... (还有 19 行)

  块 16: 第 360-376 行 (17 行)
    360: .mt-2 { margin-top: 0.5rem; }
    361: .mt-3 { margin-top: 1rem; }
    362: .mt-4 { margin-top: 1.5rem; }
    ... (还有 14 行)


统计信息:
  覆盖率: 93.7%
  块中总行数: 354
  结构类型数: 0
