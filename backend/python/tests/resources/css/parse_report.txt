FileParser 解析结果报告 - CSS
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css
  文件名: sample.css
  内容长度: 6499 字符
  行数: 378

关键结构行 (0 个):
  无检测到关键结构

检测到的结构类型:
  无结构类型检测到

代码块信息 (26 个):
  块 1: 第 5-17 行 (13 行)
      5:     --primary-color: #3498db;
      6:     --secondary-color: #2ecc71;
      7:     --accent-color: #e74c3c;
    ... (还有 10 行)

  块 2: 第 18-31 行 (14 行)
     18:     padding: 0;
     19:     box-sizing: border-box;
     20: }
    ... (还有 11 行)

  块 3: 第 32-42 行 (11 行)
     32:     font-weight: 600;
     33: }
     34: 
    ... (还有 8 行)

  块 4: 第 46-57 行 (12 行)
     46:     color: var(--secondary-color);
     47:     text-decoration: underline;
     48: }
    ... (还有 9 行)

  块 5: 第 58-68 行 (11 行)
     58:     margin: 0 auto;
     59:     padding: 0 20px;
     60: }
    ... (还有 8 行)

  块 6: 第 69-81 行 (13 行)
     69:     cursor: pointer;
     70:     transition: var(--transition);
     71:     font-size: 16px;
    ... (还有 10 行)

  块 7: 第 85-96 行 (12 行)
     85:     background-color: var(--accent-color);
     86: }
     87: 
    ... (还有 9 行)

  块 8: 第 97-111 行 (15 行)
     97:     padding: 2rem 0;
     98: }
     99: 
    ... (还有 12 行)

  块 9: 第 112-123 行 (12 行)
    112:     padding: 12px;
    113:     border: 1px solid #ddd;
    114:     border-radius: var(--border-radius);
    ... (还有 9 行)

  块 10: 第 127-138 行 (12 行)
    127:     content: " (PDF)";
    128:     font-size: 0.8em;
    129:     color: #666;
    ... (还有 9 行)

  块 11: 第 142-155 行 (14 行)
    142:     background-color: #f9f9f9;
    143: }
    144: 
    ... (还有 11 行)

  块 12: 第 162-174 行 (13 行)
    162:     font-size: 2em;
    163:     color: var(--primary-color);
    164: }
    ... (还有 10 行)

  块 13: 第 175-185 行 (11 行)
    175:     padding: 1rem;
    176:     background-color: #f9f9f9;
    177:     border-radius: var(--border-radius);
    ... (还有 8 行)

  块 14: 第 186-196 行 (11 行)
    186:     padding-top: 2rem;
    187: }
    188: 
    ... (还有 8 行)

  块 15: 第 197-207 行 (11 行)
    197:     align-items: center;
    198:     flex-wrap: wrap;
    199: }
    ... (还有 8 行)

  块 16: 第 212-222 行 (11 行)
    212:     display: grid;
    213:     grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    214:     grid-gap: 2rem;
    ... (还有 8 行)

  块 17: 第 226-237 行 (12 行)
    226: @keyframes fadeIn {
    227:     from {
    228:         opacity: 0;
    ... (还有 9 行)

  块 18: 第 239-251 行 (13 行)
    239:         transform: translateX(-100%);
    240:     }
    241:     100% {
    ... (还有 10 行)

  块 19: 第 257-269 行 (13 行)
    257:         padding: 0 15px;
    258:     }
    259:     
    ... (还有 10 行)

  块 20: 第 273-283 行 (11 行)
    273:         display: block;
    274:     }
    275: }
    ... (还有 8 行)

  块 21: 第 289-299 行 (11 行)
    289:         display: none;
    290:     }
    291:     
    ... (还有 8 行)

  块 22: 第 305-318 行 (14 行)
    305:     width: calc(100% - 40px);
    306:     margin: 0 20px;
    307: }
    ... (还有 11 行)

  块 23: 第 323-337 行 (15 行)
    323:     cursor: pointer;
    324: }
    325: 
    ... (还有 12 行)

  块 24: 第 338-350 行 (13 行)
    338:     border-radius: var(--border-radius);
    339:     
    340:     &:hover {
    ... (还有 10 行)

  块 25: 第 355-365 行 (11 行)
    355: .text-center { text-align: center; }
    356: .text-left { text-align: left; }
    357: .text-right { text-align: right; }
    ... (还有 8 行)

  块 26: 第 368-368 行 (1 行)
    368:     position: absolute;


统计信息:
  覆盖率: 82.0%
  块中总行数: 310
  结构类型数: 0
