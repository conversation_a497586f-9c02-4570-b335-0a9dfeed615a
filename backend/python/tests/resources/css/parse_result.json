{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "filename": "sample.css", "content_length": 6499, "line_count": 378}, "parsing_results": {"key_structure_lines": {}, "key_structure_count": 0, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 5, "end_line": 17, "content": "    --primary-color: #3498db;\n    --secondary-color: #2ecc71;\n    --accent-color: #e74c3c;\n    --text-color: #333;\n    --background-color: #fff;\n    --border-radius: 8px;\n    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n    --transition: all 0.3s ease;\n}\n\n/* Universal selector */\n* {\n    margin: 0;", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 18, "end_line": 31, "content": "    padding: 0;\n    box-sizing: border-box;\n}\n\n/* Element selectors */\nbody {\n    font-family: 'Arial', sans-serif;\n    line-height: 1.6;\n    color: var(--text-color);\n    background-color: var(--background-color);\n}\n\nh1, h2, h3, h4, h5, h6 {\n    margin-bottom: 1rem;", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 32, "end_line": 42, "content": "    font-weight: 600;\n}\n\np {\n    margin-bottom: 1rem;\n}\n\na {\n    color: var(--primary-color);\n    text-decoration: none;\n    transition: var(--transition);", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 46, "end_line": 57, "content": "    color: var(--secondary-color);\n    text-decoration: underline;\n}\n\nimg {\n    max-width: 100%;\n    height: auto;\n}\n\n/* Class selectors */\n.container {\n    max-width: 1200px;", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 58, "end_line": 68, "content": "    margin: 0 auto;\n    padding: 0 20px;\n}\n\n.btn {\n    display: inline-block;\n    padding: 12px 24px;\n    background-color: var(--primary-color);\n    color: white;\n    border: none;\n    border-radius: var(--border-radius);", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 69, "end_line": 81, "content": "    cursor: pointer;\n    transition: var(--transition);\n    font-size: 16px;\n}\n\n.btn:hover {\n    background-color: var(--secondary-color);\n    transform: translateY(-2px);\n    box-shadow: var(--box-shadow);\n}\n\n.btn-secondary {\n    background-color: var(--secondary-color);", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 85, "end_line": 96, "content": "    background-color: var(--accent-color);\n}\n\n/* ID selectors */\n#header {\n    background-color: var(--primary-color);\n    color: white;\n    padding: 1rem 0;\n}\n\n#main-content {\n    min-height: calc(100vh - 200px);", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 97, "end_line": 111, "content": "    padding: 2rem 0;\n}\n\n#footer {\n    background-color: #333;\n    color: white;\n    text-align: center;\n    padding: 2rem 0;\n}\n\n/* Attribute selectors */\ninput[type=\"text\"],\ninput[type=\"email\"],\ninput[type=\"password\"] {\n    width: 100%;", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 112, "end_line": 123, "content": "    padding: 12px;\n    border: 1px solid #ddd;\n    border-radius: var(--border-radius);\n    font-size: 16px;\n}\n\ninput[required] {\n    border-left: 3px solid var(--accent-color);\n}\n\na[href^=\"http\"] {\n    color: var(--accent-color);", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 127, "end_line": 138, "content": "    content: \" (PDF)\";\n    font-size: 0.8em;\n    color: #666;\n}\n\n/* Pseudo-classes */\n.nav-item:first-child {\n    margin-left: 0;\n}\n\n.nav-item:last-child {\n    margin-right: 0;", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 142, "end_line": 155, "content": "    background-color: #f9f9f9;\n}\n\n.nav-item:nth-child(even) {\n    background-color: #fff;\n}\n\n.form-group:focus-within {\n    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);\n}\n\n/* Pseudo-elements */\n.quote::before {\n    content: \"\"\";", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 162, "end_line": 174, "content": "    font-size: 2em;\n    color: var(--primary-color);\n}\n\n.clearfix::after {\n    content: \"\";\n    display: table;\n    clear: both;\n}\n\n/* Combinators */\n.sidebar > .widget {\n    margin-bottom: 2rem;", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 175, "end_line": 185, "content": "    padding: 1rem;\n    background-color: #f9f9f9;\n    border-radius: var(--border-radius);\n}\n\n.nav-menu li + li {\n    margin-left: 1rem;\n}\n\n.article ~ .article {\n    border-top: 1px solid #eee;", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 186, "end_line": 196, "content": "    padding-top: 2rem;\n}\n\n.header .logo + .nav {\n    margin-left: auto;\n}\n\n/* Flexbox */\n.flex-container {\n    display: flex;\n    justify-content: space-between;", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 197, "end_line": 207, "content": "    align-items: center;\n    flex-wrap: wrap;\n}\n\n.flex-item {\n    flex: 1;\n    margin: 0 10px;\n}\n\n.flex-item:first-child {\n    flex: 2;", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 212, "end_line": 222, "content": "    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n    grid-gap: 2rem;\n    padding: 2rem 0;\n}\n\n.grid-item {\n    background-color: white;\n    padding: 1.5rem;\n    border-radius: var(--border-radius);\n    box-shadow: var(--box-shadow);", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 226, "end_line": 237, "content": "@keyframes fadeIn {\n    from {\n        opacity: 0;\n        transform: translateY(20px);\n    }\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n@keyframes slideIn {", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 239, "end_line": 251, "content": "        transform: translateX(-100%);\n    }\n    100% {\n        transform: translateX(0);\n    }\n}\n\n.fade-in {\n    animation: fadeIn 0.6s ease-out;\n}\n\n.slide-in {\n    animation: slideIn 0.5s ease-out;", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 257, "end_line": 269, "content": "        padding: 0 15px;\n    }\n    \n    .flex-container {\n        flex-direction: column;\n    }\n    \n    .grid-container {\n        grid-template-columns: 1fr;\n    }\n    \n    .nav-menu {\n        display: none;", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 273, "end_line": 283, "content": "        display: block;\n    }\n}\n\n@media screen and (min-width: 769px) and (max-width: 1024px) {\n    .container {\n        max-width: 960px;\n    }\n    \n    .grid-container {\n        grid-template-columns: repeat(2, 1fr);", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 289, "end_line": 299, "content": "        display: none;\n    }\n    \n    body {\n        font-size: 12pt;\n        line-height: 1.4;\n    }\n    \n    a {\n        color: black;\n        text-decoration: underline;", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 305, "end_line": 318, "content": "    width: calc(100% - 40px);\n    margin: 0 20px;\n}\n\n.gradient-background {\n    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));\n}\n\n.radial-gradient {\n    background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(52,152,219,1) 100%);\n}\n\n.transform-example {\n    transform: rotate(45deg) scale(1.2) translateX(10px);", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 323, "end_line": 337, "content": "    cursor: pointer;\n}\n\n.input-group:has(input:invalid) {\n    border-color: var(--accent-color);\n}\n\n.container:where(.dark-theme, .high-contrast) {\n    background-color: #222;\n    color: white;\n}\n\n/* CSS Nesting (future syntax) */\n.card {\n    padding: 1rem;", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 338, "end_line": 350, "content": "    border-radius: var(--border-radius);\n    \n    &:hover {\n        box-shadow: var(--box-shadow);\n    }\n    \n    .card-title {\n        font-size: 1.5rem;\n        margin-bottom: 0.5rem;\n    }\n    \n    .card-content {\n        color: #666;", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 355, "end_line": 365, "content": ".text-center { text-align: center; }\n.text-left { text-align: left; }\n.text-right { text-align: right; }\n\n.mt-1 { margin-top: 0.25rem; }\n.mt-2 { margin-top: 0.5rem; }\n.mt-3 { margin-top: 1rem; }\n.mt-4 { margin-top: 1.5rem; }\n\n.hidden { display: none; }\n.visible { display: block; }", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 368, "end_line": 368, "content": "    position: absolute;", "line_count": 1}], "chunk_count": 26}, "analysis": {"detected_structures": [], "structure_types_count": 0, "total_lines_in_chunks": 310, "coverage_percentage": 82.01058201058201}}