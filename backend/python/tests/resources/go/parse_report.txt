FileParser 解析结果报告 - GO
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go
  文件名: sample.go
  内容长度: 6803 字符
  行数: 313

关键结构行 (8 个):
  第  68 行: name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {
           内容: func NewUserService(repo UserRepository, logger Logger) *UserService {
  第 142 行: name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {
           内容: func Map[T, U any](slice []T, fn func(T) U) []U {
  第 158 行: name.definition.function: func Sum[T Numeric](values []T) T {
           内容: func Sum[T Numeric](values []T) T {
  第 236 行: name.definition.function: func LogFields(msg string, fields ...interface{}) {
           内容: func LogFields(msg string, fields ...interface{}) {
  第 241 行: name.definition.function: func SafeOperation() (err error) {
           内容: func SafeOperation() (err error) {
  第 253 行: name.definition.function: func riskyOperation() {
           内容: func riskyOperation() {
  第 259 行: name.definition.function: func init() {
           内容: func init() {
  第 264 行: name.definition.function: func main() {
           内容: func main() {

检测到的结构类型:
  - name.definition.function: func LogFields(msg string, fields ...interface{}) {: 1 个
  - name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {: 1 个
  - name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {: 1 个
  - name.definition.function: func SafeOperation() (err error) {: 1 个
  - name.definition.function: func Sum[T Numeric](values []T) T {: 1 个
  - name.definition.function: func init() {: 1 个
  - name.definition.function: func main() {: 1 个
  - name.definition.function: func riskyOperation() {: 1 个

代码块信息 (14 个):
  块 1: 第 3-17 行 (15 行)
      3: package main
      4: 
      5: import (
    ... (还有 12 行)

  块 2: 第 18-28 行 (11 行)
     18: 	Timeout    = 30 * time.Second
     19: )
     20: 
    ... (还有 8 行)

  块 3: 第 32-47 行 (16 行)
     32: type User struct {
     33: 	ID       UserID    `json:"id"`
     34: 	Name     string    `json:"name"`
    ... (还有 13 行)

  块 4: 第 54-68 行 (15 行)
     54: type Logger interface {
     55: 	Info(msg string, fields ...interface{})
     56: 	Error(msg string, err error, fields ...interface{})
    ... (还有 12 行)

  块 5: 第 77-99 行 (23 行)
     77: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {
     78: 	s.mu.RLock()
     79: 	if user, exists := s.cache[id]; exists {
    ... (还有 20 行)

  块 6: 第 125-136 行 (12 行)
    125: func (s *UserService) validateUser(user *User) error {
    126: 	if user.Name == "" {
    127: 		return fmt.Errorf("name is required")
    ... (还有 9 行)

  块 7: 第 142-158 行 (17 行)
    142: func Map[T, U any](slice []T, fn func(T) U) []U {
    143: 	result := make([]U, len(slice))
    144: 	for i, v := range slice {
    ... (还有 14 行)

  块 8: 第 159-199 行 (41 行)
    159: 	var sum T
    160: 	for _, v := range values {
    161: 		sum += v
    ... (还有 38 行)

  块 9: 第 171-195 行 (25 行)
    171: 		defer close(userChan)
    172: 		
    173: 		var wg sync.WaitGroup
    ... (还有 22 行)

  块 10: 第 179-194 行 (16 行)
    179: 				defer wg.Done()
    180: 				
    181: 				semaphore <- struct{}{} // Acquire
    ... (还有 13 行)

  块 11: 第 205-215 行 (11 行)
    205: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {
    206: 	ctx := r.Context()
    207: 	
    ... (还有 8 行)

  块 12: 第 236-246 行 (11 行)
    236: func LogFields(msg string, fields ...interface{}) {
    237: 	fmt.Printf("%s: %v\n", msg, fields)
    238: }
    ... (还有 8 行)

  块 13: 第 253-264 行 (12 行)
    253: func riskyOperation() {
    254: 	// This might panic
    255: 	panic("something went wrong")
    ... (还有 9 行)

  块 14: 第 268-268 行 (1 行)
    268: 	var repo UserRepository // Would be implemented


统计信息:
  覆盖率: 72.2%
  块中总行数: 226
  结构类型数: 8
