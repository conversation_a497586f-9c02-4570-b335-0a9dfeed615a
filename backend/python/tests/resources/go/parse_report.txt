FileParser 解析结果报告 - GO
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go
  文件名: sample.go
  内容长度: 6803 字符
  行数: 313

关键结构行 (14 个):
  第  68 行: name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {
           内容: func NewUserService(repo UserRepository, logger Logger) *UserService {
  第  77 行: name.definition.method: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {
           内容: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {
  第  99 行: name.definition.method: func (s *UserService) CreateUser(ctx context.Context, user *User) error {
           内容: func (s *UserService) CreateUser(ctx context.Context, user *User) error {
  第 125 行: name.definition.method: func (s *UserService) validateUser(user *User) error {
           内容: func (s *UserService) validateUser(user *User) error {
  第 136 行: name.definition.method: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {
           内容: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {
  第 142 行: name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {
           内容: func Map[T, U any](slice []T, fn func(T) U) []U {
  第 158 行: name.definition.function: func Sum[T Numeric](values []T) T {
           内容: func Sum[T Numeric](values []T) T {
  第 167 行: name.definition.method: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {
           内容: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {
  第 205 行: name.definition.method: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {
           内容: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {
  第 236 行: name.definition.function: func LogFields(msg string, fields ...interface{}) {
           内容: func LogFields(msg string, fields ...interface{}) {
  第 241 行: name.definition.function: func SafeOperation() (err error) {
           内容: func SafeOperation() (err error) {
  第 253 行: name.definition.function: func riskyOperation() {
           内容: func riskyOperation() {
  第 259 行: name.definition.function: func init() {
           内容: func init() {
  第 264 行: name.definition.function: func main() {
           内容: func main() {

检测到的结构类型:
  - name.definition.function: func LogFields(msg string, fields ...interface{}) {: 1 个
  - name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {: 1 个
  - name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {: 1 个
  - name.definition.function: func SafeOperation() (err error) {: 1 个
  - name.definition.function: func Sum[T Numeric](values []T) T {: 1 个
  - name.definition.function: func init() {: 1 个
  - name.definition.function: func main() {: 1 个
  - name.definition.function: func riskyOperation() {: 1 个
  - name.definition.method: func (s *UserService) CreateUser(ctx context.Context, user *User) error {: 1 个
  - name.definition.method: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {: 1 个
  - name.definition.method: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {: 1 个
  - name.definition.method: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {: 1 个
  - name.definition.method: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {: 1 个
  - name.definition.method: func (s *UserService) validateUser(user *User) error {: 1 个

代码块信息 (11 个):
  块 1: 第 3-23 行 (21 行)
      3: package main
      4: 
      5: import (
    ... (还有 18 行)

  块 2: 第 26-47 行 (22 行)
     26: 	StatusActive   Status = "active"
     27: 	StatusInactive Status = "inactive"
     28: 	StatusPending  Status = "pending"
    ... (还有 19 行)

  块 3: 第 54-77 行 (24 行)
     54: type Logger interface {
     55: 	Info(msg string, fields ...interface{})
     56: 	Error(msg string, err error, fields ...interface{})
    ... (还有 21 行)

  块 4: 第 99-125 行 (27 行)
     99: func (s *UserService) CreateUser(ctx context.Context, user *User) error {
    100: 	if user == nil {
    101: 		return fmt.Errorf("user cannot be nil")
    ... (还有 24 行)

  块 5: 第 136-158 行 (23 行)
    136: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {
    137: 	// Implementation would go here
    138: 	return 100, 75, nil
    ... (还有 20 行)

  块 6: 第 159-199 行 (41 行)
    159: 	var sum T
    160: 	for _, v := range values {
    161: 		sum += v
    ... (还有 38 行)

  块 7: 第 171-195 行 (25 行)
    171: 		defer close(userChan)
    172: 		
    173: 		var wg sync.WaitGroup
    ... (还有 22 行)

  块 8: 第 179-205 行 (27 行)
    179: 				defer wg.Done()
    180: 				
    181: 				semaphore <- struct{}{} // Acquire
    ... (还有 24 行)

  块 9: 第 215-236 行 (22 行)
    215: 	var userID UserID
    216: 	if _, err := fmt.Sscanf(userIDStr, "%d", &userID); err != nil {
    217: 		http.Error(w, "invalid user ID", http.StatusBadRequest)
    ... (还有 19 行)

  块 10: 第 241-264 行 (24 行)
    241: func SafeOperation() (err error) {
    242: 	defer func() {
    243: 		if r := recover(); r != nil {
    ... (还有 21 行)

  块 11: 第 268-269 行 (2 行)
    268: 	var repo UserRepository // Would be implemented
    269: 	var logger Logger       // Would be implemented


统计信息:
  覆盖率: 82.4%
  块中总行数: 258
  结构类型数: 14
