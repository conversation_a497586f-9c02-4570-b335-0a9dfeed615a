{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "filename": "sample.go", "content_length": 6803, "line_count": 313}, "parsing_results": {"key_structure_lines": {"68": "name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {", "77": "name.definition.method: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {", "99": "name.definition.method: func (s *UserService) CreateUser(ctx context.Context, user *User) error {", "125": "name.definition.method: func (s *UserService) validateUser(user *User) error {", "136": "name.definition.method: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {", "142": "name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {", "158": "name.definition.function: func Sum[T Numeric](values []T) T {", "167": "name.definition.method: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {", "205": "name.definition.method: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {", "236": "name.definition.function: func LogFields(msg string, fields ...interface{}) {", "241": "name.definition.function: func SafeOperation() (err error) {", "253": "name.definition.function: func riskyOperation() {", "259": "name.definition.function: func init() {", "264": "name.definition.function: func main() {"}, "key_structure_count": 14, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 3, "end_line": 23, "content": "package main\n\nimport (\n\t\"context\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"log\"\n\t\"net/http\"\n\t\"sync\"\n\t\"time\"\n)\n\n// Constants\nconst (\n\tMaxRetries = 3\n\tTimeout    = 30 * time.Second\n)\n\n// Type definitions\ntype UserID int64\ntype Status string", "line_count": 21}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 26, "end_line": 47, "content": "\tStatusActive   Status = \"active\"\n\tStatusInactive Status = \"inactive\"\n\tStatusPending  Status = \"pending\"\n)\n\n// Struct definitions\ntype User struct {\n\tID       UserID    `json:\"id\"`\n\tName     string    `json:\"name\"`\n\tEmail    string    `json:\"email\"`\n\tStatus   Status    `json:\"status\"`\n\tCreated  time.Time `json:\"created\"`\n\tSettings *Settings `json:\"settings,omitempty\"`\n}\n\ntype Settings struct {\n\tTheme       string `json:\"theme\"`\n\tNotifications bool   `json:\"notifications\"`\n}\n\n// Interface definitions\ntype UserRepository interface {", "line_count": 22}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 54, "end_line": 77, "content": "type Logger interface {\n\tInfo(msg string, fields ...interface{})\n\tError(msg string, err error, fields ...interface{})\n}\n\n// Struct with embedded interface\ntype UserService struct {\n\trepo   UserRepository\n\tlogger Logger\n\tmu     sync.RWMutex\n\tcache  map[UserID]*User\n}\n\n// Constructor function\nfunc NewUserService(repo UserRepository, logger Logger) *UserService {\n\treturn &UserService{\n\t\trepo:   repo,\n\t\tlogger: logger,\n\t\tcache:  make(map[UserID]*User),\n\t}\n}\n\n// Method with receiver\nfunc (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {", "line_count": 24}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 99, "end_line": 125, "content": "func (s *UserService) CreateUser(ctx context.Context, user *User) error {\n\tif user == nil {\n\t\treturn fmt.<PERSON><PERSON><PERSON>(\"user cannot be nil\")\n\t}\n\n\tif err := s.validateUser(user); err != nil {\n\t\treturn fmt.Errorf(\"validation failed: %w\", err)\n\t}\n\n\tuser.Created = time.Now()\n\tuser.Status = StatusPending\n\n\tif err := s.repo.CreateUser(ctx, user); err != nil {\n\t\ts.logger.Error(\"failed to create user\", err, \"user\", user)\n\t\treturn err\n\t}\n\n\ts.mu.Lock()\n\ts.cache[user.ID] = user\n\ts.mu.Unlock()\n\n\ts.logger.Info(\"user created successfully\", \"user_id\", user.ID)\n\treturn nil\n}\n\n// Private method\nfunc (s *UserService) validateUser(user *User) error {", "line_count": 27}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 136, "end_line": 158, "content": "func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {\n\t// Implementation would go here\n\treturn 100, 75, nil\n}\n\n// Generic function (Go 1.18+)\nfunc Map[T, U any](slice []T, fn func(T) U) []U {\n\tresult := make([]U, len(slice))\n\tfor i, v := range slice {\n\t\tresult[i] = fn(v)\n\t}\n\treturn result\n}\n\n// Generic type constraint\ntype Numeric interface {\n\t~int | ~int8 | ~int16 | ~int32 | ~int64 |\n\t\t~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |\n\t\t~float32 | ~float64\n}\n\n// Generic function with constraint\nfunc Sum[T Numeric](values []T) T {", "line_count": 23}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 159, "end_line": 199, "content": "\tvar sum T\n\tfor _, v := range values {\n\t\tsum += v\n\t}\n\treturn sum\n}\n\n// Goroutine and channel usage\nfunc (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {\n\tuserChan := make(chan *User, len(userIDs))\n\t\n\tgo func() {\n\t\tdefer close(user<PERSON>han)\n\t\t\n\t\tvar wg sync.WaitGroup\n\t\tsemaphore := make(chan struct{}, 10) // Limit concurrent goroutines\n\t\t\n\t\tfor _, id := range userIDs {\n\t\t\twg.Add(1)\n\t\t\tgo func(userID UserID) {\n\t\t\t\tdefer wg.Done()\n\t\t\t\t\n\t\t\t\tsemaphore <- struct{}{} // Acquire\n\t\t\t\tdefer func() { <-semaphore }() // Release\n\t\t\t\t\n\t\t\t\tuser, err := s.GetUser(ctx, userID)\n\t\t\t\tif err != nil {\n\t\t\t\t\ts.logger.Error(\"failed to process user\", err, \"user_id\", userID)\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tselect {\n\t\t\t\tcase userChan <- user:\n\t\t\t\tcase <-ctx.Done():\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t}(id)\n\t\t}\n\t\t\n\t\twg.Wait()\n\t}()", "line_count": 41}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 205, "end_line": 236, "content": "func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {\n\tctx := r.Context()\n\t\n\t// Extract user ID from URL or query params\n\tuserIDStr := r.URL.Query().Get(\"id\")\n\tif userIDStr == \"\" {\n\t\thttp.Error(w, \"user ID is required\", http.StatusBadRequest)\n\t\treturn\n\t}\n\t\n\tvar userID UserID\n\tif _, err := fmt.Sscanf(userIDStr, \"%d\", &userID); err != nil {\n\t\thttp.Error(w, \"invalid user ID\", http.StatusBadRequest)\n\t\treturn\n\t}\n\t\n\tuser, err := s.GetUser(ctx, userID)\n\tif err != nil {\n\t\thttp.Error(w, \"user not found\", http.StatusNotFound)\n\t\treturn\n\t}\n\t\n\tw.Header().Set(\"Content-Type\", \"application/json\")\n\tif err := json.NewEncoder(w).Encode(user); err != nil {\n\t\ts.logger.Error(\"failed to encode user\", err, \"user_id\", userID)\n\t\thttp.Error(w, \"internal server error\", http.StatusInternalServerError)\n\t\treturn\n\t}\n}\n\n// Variadic function\nfunc LogFields(msg string, fields ...interface{}) {", "line_count": 32}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 241, "end_line": 264, "content": "func SafeOperation() (err error) {\n\tdefer func() {\n\t\tif r := recover(); r != nil {\n\t\t\terr = fmt.<PERSON><PERSON><PERSON>(\"panic recovered: %v\", r)\n\t\t}\n\t}()\n\t\n\t// Some operation that might panic\n\triskyOperation()\n\treturn nil\n}\n\nfunc riskyOperation() {\n\t// This might panic\n\tpanic(\"something went wrong\")\n}\n\n// Init function\nfunc init() {\n\tlog.Println(\"Package initialized\")\n}\n\n// Main function\nfunc main() {", "line_count": 24}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 268, "end_line": 269, "content": "\tvar repo UserRepository // Would be implemented\n\tvar logger Logger       // Would be implemented", "line_count": 2}], "chunk_count": 9}, "analysis": {"detected_structures": ["name.definition.method: func (s *UserService) validateUser(user *User) error {", "name.definition.function: func SafeOperation() (err error) {", "name.definition.function: func main() {", "name.definition.function: func Sum[T Numeric](values []T) T {", "name.definition.function: func init() {", "name.definition.method: func (s *UserService) CreateUser(ctx context.Context, user *User) error {", "name.definition.method: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {", "name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {", "name.definition.function: func LogFields(msg string, fields ...interface{}) {", "name.definition.method: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {", "name.definition.function: func riskyOperation() {", "name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {", "name.definition.method: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {", "name.definition.method: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {"], "structure_types_count": 14, "total_lines_in_chunks": 216, "coverage_percentage": 69.00958466453673}}