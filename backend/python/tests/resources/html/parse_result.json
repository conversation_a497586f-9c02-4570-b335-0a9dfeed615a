{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "filename": "sample.html", "content_length": 8420, "line_count": 208}, "parsing_results": {"key_structure_lines": {}, "key_structure_count": 0, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 0, "end_line": 0, "content": "<!DOCTYPE html>", "line_count": 1}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 1, "end_line": 15, "content": "<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta name=\"description\" content=\"HTML sample file for testing FileParser functionality\">\n    <title>Sample HTML Document</title>\n    <link rel=\"stylesheet\" href=\"styles.css\">\n    <link rel=\"icon\" href=\"favicon.ico\" type=\"image/x-icon\">\n    <style>\n        .inline-style {\n            color: red;\n            font-weight: bold;\n        }\n    </style>\n</head>", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 2, "end_line": 14, "content": "<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta name=\"description\" content=\"HTML sample file for testing FileParser functionality\">\n    <title>Sample HTML Document</title>\n    <link rel=\"stylesheet\" href=\"styles.css\">\n    <link rel=\"icon\" href=\"favicon.ico\" type=\"image/x-icon\">\n    <style>\n        .inline-style {\n            color: red;\n            font-weight: bold;\n        }\n    </style>", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 9, "end_line": 14, "content": "    <style>\n        .inline-style {\n            color: red;\n            font-weight: bold;\n        }\n    </style>", "line_count": 6}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 16, "end_line": 30, "content": "<body>\n    <!-- Header section -->\n    <header class=\"main-header\">\n        <nav class=\"navigation\">\n            <div class=\"logo\">\n                <img src=\"logo.png\" alt=\"Company Logo\" width=\"100\" height=\"50\">\n            </div>\n            <ul class=\"nav-menu\">\n                <li><a href=\"#home\" class=\"nav-link active\">Home</a></li>\n                <li><a href=\"#about\" class=\"nav-link\">About</a></li>\n                <li><a href=\"#services\" class=\"nav-link\">Services</a></li>\n                <li><a href=\"#contact\" class=\"nav-link\">Contact</a></li>\n            </ul>\n        </nav>\n    </header>", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 18, "end_line": 29, "content": "    <header class=\"main-header\">\n        <nav class=\"navigation\">\n            <div class=\"logo\">\n                <img src=\"logo.png\" alt=\"Company Logo\" width=\"100\" height=\"50\">\n            </div>\n            <ul class=\"nav-menu\">\n                <li><a href=\"#home\" class=\"nav-link active\">Home</a></li>\n                <li><a href=\"#about\" class=\"nav-link\">About</a></li>\n                <li><a href=\"#services\" class=\"nav-link\">Services</a></li>\n                <li><a href=\"#contact\" class=\"nav-link\">Contact</a></li>\n            </ul>\n        </nav>", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 19, "end_line": 32, "content": "        <nav class=\"navigation\">\n            <div class=\"logo\">\n                <img src=\"logo.png\" alt=\"Company Logo\" width=\"100\" height=\"50\">\n            </div>\n            <ul class=\"nav-menu\">\n                <li><a href=\"#home\" class=\"nav-link active\">Home</a></li>\n                <li><a href=\"#about\" class=\"nav-link\">About</a></li>\n                <li><a href=\"#services\" class=\"nav-link\">Services</a></li>\n                <li><a href=\"#contact\" class=\"nav-link\">Contact</a></li>\n            </ul>\n        </nav>\n    </header>\n\n    <!-- Main content -->", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 33, "end_line": 44, "content": "    <main class=\"main-content\">\n        <!-- Hero section -->\n        <section id=\"home\" class=\"hero-section\">\n            <div class=\"container\">\n                <h1 class=\"hero-title\">Welcome to Our Website</h1>\n                <p class=\"hero-description\">\n                    This is a sample HTML document containing various HTML elements\n                    for testing the FileParser functionality.\n                </p>\n                <button class=\"cta-button\" onclick=\"handleClick()\">Get Started</button>\n            </div>\n        </section>", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 35, "end_line": 46, "content": "        <section id=\"home\" class=\"hero-section\">\n            <div class=\"container\">\n                <h1 class=\"hero-title\">Welcome to Our Website</h1>\n                <p class=\"hero-description\">\n                    This is a sample HTML document containing various HTML elements\n                    for testing the FileParser functionality.\n                </p>\n                <button class=\"cta-button\" onclick=\"handleClick()\">Get Started</button>\n            </div>\n        </section>\n\n        <!-- About section -->", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 47, "end_line": 64, "content": "        <section id=\"about\" class=\"about-section\">\n            <div class=\"container\">\n                <h2>About Us</h2>\n                <div class=\"about-content\">\n                    <div class=\"about-text\">\n                        <p>We are a company dedicated to providing excellent services.</p>\n                        <p>Our team consists of experienced professionals who are passionate about their work.</p>\n                        <ul>\n                            <li>Quality service</li>\n                            <li>Customer satisfaction</li>\n                            <li>Innovation</li>\n                        </ul>\n                    </div>\n                    <div class=\"about-image\">\n                        <img src=\"about-us.jpg\" alt=\"About Us\" class=\"responsive-image\">\n                    </div>\n                </div>\n            </div>", "line_count": 18}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 48, "end_line": 63, "content": "            <div class=\"container\">\n                <h2>About Us</h2>\n                <div class=\"about-content\">\n                    <div class=\"about-text\">\n                        <p>We are a company dedicated to providing excellent services.</p>\n                        <p>Our team consists of experienced professionals who are passionate about their work.</p>\n                        <ul>\n                            <li>Quality service</li>\n                            <li>Customer satisfaction</li>\n                            <li>Innovation</li>\n                        </ul>\n                    </div>\n                    <div class=\"about-image\">\n                        <img src=\"about-us.jpg\" alt=\"About Us\" class=\"responsive-image\">\n                    </div>\n                </div>", "line_count": 16}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 50, "end_line": 62, "content": "                <div class=\"about-content\">\n                    <div class=\"about-text\">\n                        <p>We are a company dedicated to providing excellent services.</p>\n                        <p>Our team consists of experienced professionals who are passionate about their work.</p>\n                        <ul>\n                            <li>Quality service</li>\n                            <li>Customer satisfaction</li>\n                            <li>Innovation</li>\n                        </ul>\n                    </div>\n                    <div class=\"about-image\">\n                        <img src=\"about-us.jpg\" alt=\"About Us\" class=\"responsive-image\">\n                    </div>", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 60, "end_line": 89, "content": "                    <div class=\"about-image\">\n                        <img src=\"about-us.jpg\" alt=\"About Us\" class=\"responsive-image\">\n                    </div>\n                </div>\n            </div>\n        </section>\n\n        <!-- Services section -->\n        <section id=\"services\" class=\"services-section\">\n            <div class=\"container\">\n                <h2>Our Services</h2>\n                <div class=\"services-grid\">\n                    <article class=\"service-card\">\n                        <h3>Web Development</h3>\n                        <p>We create modern, responsive websites using the latest technologies.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                    <article class=\"service-card\">\n                        <h3>Mobile Apps</h3>\n                        <p>Native and cross-platform mobile applications for iOS and Android.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                    <article class=\"service-card\">\n                        <h3>Consulting</h3>\n                        <p>Technical consulting and architecture design for your projects.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                </div>\n            </div>\n        </section>", "line_count": 30}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 68, "end_line": 88, "content": "        <section id=\"services\" class=\"services-section\">\n            <div class=\"container\">\n                <h2>Our Services</h2>\n                <div class=\"services-grid\">\n                    <article class=\"service-card\">\n                        <h3>Web Development</h3>\n                        <p>We create modern, responsive websites using the latest technologies.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                    <article class=\"service-card\">\n                        <h3>Mobile Apps</h3>\n                        <p>Native and cross-platform mobile applications for iOS and Android.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                    <article class=\"service-card\">\n                        <h3>Consulting</h3>\n                        <p>Technical consulting and architecture design for your projects.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                </div>\n            </div>", "line_count": 21}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 69, "end_line": 87, "content": "            <div class=\"container\">\n                <h2>Our Services</h2>\n                <div class=\"services-grid\">\n                    <article class=\"service-card\">\n                        <h3>Web Development</h3>\n                        <p>We create modern, responsive websites using the latest technologies.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                    <article class=\"service-card\">\n                        <h3>Mobile Apps</h3>\n                        <p>Native and cross-platform mobile applications for iOS and Android.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                    <article class=\"service-card\">\n                        <h3>Consulting</h3>\n                        <p>Technical consulting and architecture design for your projects.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                </div>", "line_count": 19}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 71, "end_line": 81, "content": "                <div class=\"services-grid\">\n                    <article class=\"service-card\">\n                        <h3>Web Development</h3>\n                        <p>We create modern, responsive websites using the latest technologies.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                    <article class=\"service-card\">\n                        <h3>Mobile Apps</h3>\n                        <p>Native and cross-platform mobile applications for iOS and Android.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 77, "end_line": 91, "content": "                    <article class=\"service-card\">\n                        <h3>Mobile Apps</h3>\n                        <p>Native and cross-platform mobile applications for iOS and Android.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                    <article class=\"service-card\">\n                        <h3>Consulting</h3>\n                        <p>Technical consulting and architecture design for your projects.</p>\n                        <a href=\"#\" class=\"service-link\">Learn More</a>\n                    </article>\n                </div>\n            </div>\n        </section>\n\n        <!-- Contact section -->", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 92, "end_line": 123, "content": "        <section id=\"contact\" class=\"contact-section\">\n            <div class=\"container\">\n                <h2>Contact Us</h2>\n                <form class=\"contact-form\" action=\"/submit\" method=\"post\">\n                    <div class=\"form-group\">\n                        <label for=\"name\">Name:</label>\n                        <input type=\"text\" id=\"name\" name=\"name\" required>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"email\">Email:</label>\n                        <input type=\"email\" id=\"email\" name=\"email\" required>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"subject\">Subject:</label>\n                        <select id=\"subject\" name=\"subject\">\n                            <option value=\"\">Select a subject</option>\n                            <option value=\"general\">General Inquiry</option>\n                            <option value=\"support\">Support</option>\n                            <option value=\"sales\">Sales</option>\n                        </select>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"message\">Message:</label>\n                        <textarea id=\"message\" name=\"message\" rows=\"5\" required></textarea>\n                    </div>\n                    <div class=\"form-group\">\n                        <input type=\"checkbox\" id=\"newsletter\" name=\"newsletter\">\n                        <label for=\"newsletter\">Subscribe to our newsletter</label>\n                    </div>\n                    <button type=\"submit\" class=\"submit-button\">Send Message</button>\n                </form>\n            </div>", "line_count": 32}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 93, "end_line": 122, "content": "            <div class=\"container\">\n                <h2>Contact Us</h2>\n                <form class=\"contact-form\" action=\"/submit\" method=\"post\">\n                    <div class=\"form-group\">\n                        <label for=\"name\">Name:</label>\n                        <input type=\"text\" id=\"name\" name=\"name\" required>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"email\">Email:</label>\n                        <input type=\"email\" id=\"email\" name=\"email\" required>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"subject\">Subject:</label>\n                        <select id=\"subject\" name=\"subject\">\n                            <option value=\"\">Select a subject</option>\n                            <option value=\"general\">General Inquiry</option>\n                            <option value=\"support\">Support</option>\n                            <option value=\"sales\">Sales</option>\n                        </select>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"message\">Message:</label>\n                        <textarea id=\"message\" name=\"message\" rows=\"5\" required></textarea>\n                    </div>\n                    <div class=\"form-group\">\n                        <input type=\"checkbox\" id=\"newsletter\" name=\"newsletter\">\n                        <label for=\"newsletter\">Subscribe to our newsletter</label>\n                    </div>\n                    <button type=\"submit\" class=\"submit-button\">Send Message</button>\n                </form>", "line_count": 30}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 95, "end_line": 112, "content": "                <form class=\"contact-form\" action=\"/submit\" method=\"post\">\n                    <div class=\"form-group\">\n                        <label for=\"name\">Name:</label>\n                        <input type=\"text\" id=\"name\" name=\"name\" required>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"email\">Email:</label>\n                        <input type=\"email\" id=\"email\" name=\"email\" required>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"subject\">Subject:</label>\n                        <select id=\"subject\" name=\"subject\">\n                            <option value=\"\">Select a subject</option>\n                            <option value=\"general\">General Inquiry</option>\n                            <option value=\"support\">Support</option>\n                            <option value=\"sales\">Sales</option>\n                        </select>\n                    </div>", "line_count": 18}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 104, "end_line": 116, "content": "                    <div class=\"form-group\">\n                        <label for=\"subject\">Subject:</label>\n                        <select id=\"subject\" name=\"subject\">\n                            <option value=\"\">Select a subject</option>\n                            <option value=\"general\">General Inquiry</option>\n                            <option value=\"support\">Support</option>\n                            <option value=\"sales\">Sales</option>\n                        </select>\n                    </div>\n                    <div class=\"form-group\">\n                        <label for=\"message\">Message:</label>\n                        <textarea id=\"message\" name=\"message\" rows=\"5\" required></textarea>\n                    </div>", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 113, "end_line": 127, "content": "                    <div class=\"form-group\">\n                        <label for=\"message\">Message:</label>\n                        <textarea id=\"message\" name=\"message\" rows=\"5\" required></textarea>\n                    </div>\n                    <div class=\"form-group\">\n                        <input type=\"checkbox\" id=\"newsletter\" name=\"newsletter\">\n                        <label for=\"newsletter\">Subscribe to our newsletter</label>\n                    </div>\n                    <button type=\"submit\" class=\"submit-button\">Send Message</button>\n                </form>\n            </div>\n        </section>\n    </main>\n\n    <!-- Sidebar -->", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 128, "end_line": 145, "content": "    <aside class=\"sidebar\">\n        <div class=\"widget\">\n            <h3>Recent Posts</h3>\n            <ul>\n                <li><a href=\"#\">How to Build Better Websites</a></li>\n                <li><a href=\"#\">The Future of Web Development</a></li>\n                <li><a href=\"#\">Mobile-First Design Principles</a></li>\n            </ul>\n        </div>\n        <div class=\"widget\">\n            <h3>Categories</h3>\n            <ul>\n                <li><a href=\"#\">Web Development</a></li>\n                <li><a href=\"#\">Mobile Apps</a></li>\n                <li><a href=\"#\">Design</a></li>\n                <li><a href=\"#\">Technology</a></li>\n            </ul>\n        </div>", "line_count": 18}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 137, "end_line": 148, "content": "        <div class=\"widget\">\n            <h3>Categories</h3>\n            <ul>\n                <li><a href=\"#\">Web Development</a></li>\n                <li><a href=\"#\">Mobile Apps</a></li>\n                <li><a href=\"#\">Design</a></li>\n                <li><a href=\"#\">Technology</a></li>\n            </ul>\n        </div>\n    </aside>\n\n    <!-- Footer -->", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 149, "end_line": 180, "content": "    <footer class=\"main-footer\">\n        <div class=\"container\">\n            <div class=\"footer-content\">\n                <div class=\"footer-section\">\n                    <h4>Company</h4>\n                    <ul>\n                        <li><a href=\"#\">About</a></li>\n                        <li><a href=\"#\">Careers</a></li>\n                        <li><a href=\"#\">Press</a></li>\n                    </ul>\n                </div>\n                <div class=\"footer-section\">\n                    <h4>Support</h4>\n                    <ul>\n                        <li><a href=\"#\">Help Center</a></li>\n                        <li><a href=\"#\">Contact</a></li>\n                        <li><a href=\"#\">Privacy Policy</a></li>\n                    </ul>\n                </div>\n                <div class=\"footer-section\">\n                    <h4>Follow Us</h4>\n                    <div class=\"social-links\">\n                        <a href=\"#\" class=\"social-link\">Facebook</a>\n                        <a href=\"#\" class=\"social-link\">Twitter</a>\n                        <a href=\"#\" class=\"social-link\">LinkedIn</a>\n                    </div>\n                </div>\n            </div>\n            <div class=\"footer-bottom\">\n                <p>&copy; 2024 Sample Company. All rights reserved.</p>\n            </div>\n        </div>", "line_count": 32}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 150, "end_line": 176, "content": "        <div class=\"container\">\n            <div class=\"footer-content\">\n                <div class=\"footer-section\">\n                    <h4>Company</h4>\n                    <ul>\n                        <li><a href=\"#\">About</a></li>\n                        <li><a href=\"#\">Careers</a></li>\n                        <li><a href=\"#\">Press</a></li>\n                    </ul>\n                </div>\n                <div class=\"footer-section\">\n                    <h4>Support</h4>\n                    <ul>\n                        <li><a href=\"#\">Help Center</a></li>\n                        <li><a href=\"#\">Contact</a></li>\n                        <li><a href=\"#\">Privacy Policy</a></li>\n                    </ul>\n                </div>\n                <div class=\"footer-section\">\n                    <h4>Follow Us</h4>\n                    <div class=\"social-links\">\n                        <a href=\"#\" class=\"social-link\">Facebook</a>\n                        <a href=\"#\" class=\"social-link\">Twitter</a>\n                        <a href=\"#\" class=\"social-link\">LinkedIn</a>\n                    </div>\n                </div>\n            </div>", "line_count": 27}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 151, "end_line": 167, "content": "            <div class=\"footer-content\">\n                <div class=\"footer-section\">\n                    <h4>Company</h4>\n                    <ul>\n                        <li><a href=\"#\">About</a></li>\n                        <li><a href=\"#\">Careers</a></li>\n                        <li><a href=\"#\">Press</a></li>\n                    </ul>\n                </div>\n                <div class=\"footer-section\">\n                    <h4>Support</h4>\n                    <ul>\n                        <li><a href=\"#\">Help Center</a></li>\n                        <li><a href=\"#\">Contact</a></li>\n                        <li><a href=\"#\">Privacy Policy</a></li>\n                    </ul>\n                </div>", "line_count": 17}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 160, "end_line": 175, "content": "                <div class=\"footer-section\">\n                    <h4>Support</h4>\n                    <ul>\n                        <li><a href=\"#\">Help Center</a></li>\n                        <li><a href=\"#\">Contact</a></li>\n                        <li><a href=\"#\">Privacy Policy</a></li>\n                    </ul>\n                </div>\n                <div class=\"footer-section\">\n                    <h4>Follow Us</h4>\n                    <div class=\"social-links\">\n                        <a href=\"#\" class=\"social-link\">Facebook</a>\n                        <a href=\"#\" class=\"social-link\">Twitter</a>\n                        <a href=\"#\" class=\"social-link\">LinkedIn</a>\n                    </div>\n                </div>", "line_count": 16}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 168, "end_line": 179, "content": "                <div class=\"footer-section\">\n                    <h4>Follow Us</h4>\n                    <div class=\"social-links\">\n                        <a href=\"#\" class=\"social-link\">Facebook</a>\n                        <a href=\"#\" class=\"social-link\">Twitter</a>\n                        <a href=\"#\" class=\"social-link\">LinkedIn</a>\n                    </div>\n                </div>\n            </div>\n            <div class=\"footer-bottom\">\n                <p>&copy; 2024 Sample Company. All rights reserved.</p>\n            </div>", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 177, "end_line": 204, "content": "            <div class=\"footer-bottom\">\n                <p>&copy; 2024 Sample Company. All rights reserved.</p>\n            </div>\n        </div>\n    </footer>\n\n    <!-- Scripts -->\n    <script>\n        function handleClick() {\n            alert('But<PERSON> clicked!');\n        }\n\n        document.addEventListener('DOMContentLoaded', function() {\n            console.log('Page loaded');\n            \n            // Add smooth scrolling\n            const links = document.querySelectorAll('a[href^=\"#\"]');\n            links.forEach(link => {\n                link.addEventListener('click', function(e) {\n                    e.preventDefault();\n                    const target = document.querySelector(this.getAttribute('href'));\n                    if (target) {\n                        target.scrollIntoView({ behavior: 'smooth' });\n                    }\n                });\n            });\n        });\n    </script>", "line_count": 28}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html", "start_line": 205, "end_line": 205, "content": "    <script src=\"main.js\"></script>", "line_count": 1}], "chunk_count": 31}, "analysis": {"detected_structures": [], "structure_types_count": 0, "total_lines_in_chunks": 514, "coverage_percentage": 247.1153846153846}}