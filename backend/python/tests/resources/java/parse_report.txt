FileParser 解析结果报告 - JAVA
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/java/Sample.java
  文件名: Sample.java
  内容长度: 4306 字符
  行数: 180

关键结构行 (7 个):
  第  12 行: name.definition.class: public class Sample {
           内容: public class Sample {
  第  68 行: name.definition.class: public class InnerClass {
           内容: public class InnerClass {
  第  81 行: name.definition.class: public static class StaticNestedClass {
           内容: public static class StaticNestedClass {
  第  91 行: name.definition.interface: interface Drawable {
           内容: interface Drawable {
  第 104 行: name.definition.enum: enum Color {
           内容: enum Color {
  第 147 行: name.definition.class: abstract class Shape {
           内容: abstract class Shape {
  第 162 行: name.definition.class: class Circle extends Shape implements Drawable {
           内容: class Circle extends Shape implements Drawable {

检测到的结构类型:
  - name.definition.class: abstract class Shape {: 1 个
  - name.definition.class: class Circle extends Shape implements Drawable {: 1 个
  - name.definition.class: public class InnerClass {: 1 个
  - name.definition.class: public class Sample {: 1 个
  - name.definition.class: public static class StaticNestedClass {: 1 个
  - name.definition.enum: enum Color {: 1 个
  - name.definition.interface: interface Drawable {: 1 个

代码块信息 (19 个):
  块 1: 第 0-11 行 (12 行)
      0: package com.example.test;
      1: 
      2: import java.util.*;
    ... (还有 9 行)

  块 2: 第 12-25 行 (14 行)
     12: public class Sample {
     13:     
     14:     // Field declarations
    ... (还有 11 行)

  块 3: 第 21-32 行 (12 行)
     21:     public Sample() {
     22:         this.instanceField = 0;
     23:         this.protectedField = new ArrayList<>();
    ... (还有 9 行)

  块 4: 第 28-41 行 (14 行)
     28:     public Sample(int value, List<String> items) {
     29:         this.instanceField = value;
     30:         this.protectedField = new ArrayList<>(items);
    ... (还有 11 行)

  块 5: 第 39-54 行 (16 行)
     39:     private int privateMethod(int a, int b) {
     40:         return a + b;
     41:     }
    ... (还有 13 行)

  块 6: 第 48-65 行 (18 行)
     48:     public <T> List<T> genericMethod(T item, int count) {
     49:         List<T> result = new ArrayList<>();
     50:         for (int i = 0; i < count; i++) {
    ... (还有 15 行)

  块 7: 第 57-67 行 (11 行)
     57:     public void lambdaExamples() {
     58:         Function<String, Integer> stringLength = s -> s.length();
     59:         Function<Integer, Integer> square = x -> x * x;
    ... (还有 8 行)

  块 8: 第 68-80 行 (13 行)
     68:     public class InnerClass {
     69:         private String innerField;
     70:         
    ... (还有 10 行)

  块 9: 第 81-101 行 (21 行)
     81:     public static class StaticNestedClass {
     82:         private static int staticNestedField = 100;
     83:         
    ... (还有 18 行)

  块 10: 第 91-103 行 (13 行)
     91: interface Drawable {
     92:     void draw();
     93:     
    ... (还有 10 行)

  块 11: 第 104-117 行 (14 行)
    104: enum Color {
    105:     RED("Red Color"),
    106:     GREEN("Green Color"),
    ... (还有 11 行)

  块 12: 第 115-138 行 (24 行)
    115:     public String getDescription() {
    116:         return description;
    117:     }
    ... (还有 21 行)

  块 13: 第 121-132 行 (12 行)
    121: record Person(String name, int age, String email) {
    122:     // Compact constructor
    123:     public Person {
    ... (还有 9 行)

  块 14: 第 130-140 行 (11 行)
    130:     public Person(String name, int age) {
    131:         this(name, age, null);
    132:     }
    ... (还有 8 行)

  块 15: 第 141-159 行 (19 行)
    141: @interface MyAnnotation {
    142:     String value() default "";
    143:     int priority() default 0;
    ... (还有 16 行)

  块 16: 第 147-158 行 (12 行)
    147: abstract class Shape {
    148:     protected String color;
    149:     
    ... (还有 9 行)

  块 17: 第 156-179 行 (24 行)
    156:     public String getColor() {
    157:         return color;
    158:     }
    ... (还有 21 行)

  块 18: 第 162-173 行 (12 行)
    162: class Circle extends Shape implements Drawable {
    163:     private double radius;
    164:     
    ... (还有 9 行)

  块 19: 第 171-171 行 (1 行)
    171:     public double getArea() {


统计信息:
  覆盖率: 151.7%
  块中总行数: 273
  结构类型数: 7
