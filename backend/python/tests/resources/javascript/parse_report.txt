FileParser 解析结果报告 - JAVASCRIPT
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js
  文件名: sample.js
  内容长度: 4612 字符
  行数: 220

关键结构行 (17 个):
  第  20 行: definition.function: function regularFunction(param1, param2) {
           内容: function regularFunction(param1, param2) {
  第  25 行: definition.function: const arrowFunction = (x, y) => x * y;
           内容: const arrowFunction = (x, y) => x * y;
  第  26 行: definition.function: const singleParamArrow = x => x * 2;
           内容: const singleParamArrow = x => x * 2;
  第  27 行: definition.function: const noParamArrow = () => 'no params';
           内容: const noParamArrow = () => 'no params';
  第  30 行: definition.function: async function fetchData(url) {
           内容: async function fetchData(url) {
  第  40 行: definition.function: const asyncArrowFunction = async (id) => {
           内容: const asyncArrowFunction = async (id) => {
  第  46 行: definition.function: function* numberGenerator(max) {
           内容: function* numberGenerator(max) {
  第  82 行: definition.class: class Animal {
           内容: class Animal {
  第 110 行: definition.class: class Dog extends Animal {
           内容: class Dog extends Animal {
  第 134 行: definition.function: const functionExpression = function(x) {
           内容: const functionExpression = function(x) {
  第 138 行: definition.function: const namedFunctionExpression = function square(x) {
           内容: const namedFunctionExpression = function square(x) {
  第 148 行: definition.function: const higherOrderFunction = (callback) => {
           内容: const higherOrderFunction = (callback) => {
  第 164 行: definition.function: function sumAll(...args) {
           内容: function sumAll(...args) {
  第 169 行: definition.function: function greetUser(name = 'Guest', greeting = 'Hello') {
           内容: function greetUser(name = 'Guest', greeting = 'Hello') {
  第 174 行: definition.function: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {
           内容: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {
  第 195 行: definition.function: function privateFunction() {
           内容: function privateFunction() {
  第 211 行: definition.class: export default class DefaultExport {
           内容: export default class DefaultExport {

检测到的结构类型:
  - definition.class: class Animal {: 1 个
  - definition.class: class Dog extends Animal {: 1 个
  - definition.class: export default class DefaultExport {: 1 个
  - definition.function: async function fetchData(url) {: 1 个
  - definition.function: const arrowFunction = (x, y) => x * y;: 1 个
  - definition.function: const asyncArrowFunction = async (id) => {: 1 个
  - definition.function: const functionExpression = function(x) {: 1 个
  - definition.function: const higherOrderFunction = (callback) => {: 1 个
  - definition.function: const namedFunctionExpression = function square(x) {: 1 个
  - definition.function: const noParamArrow = () => 'no params';: 1 个
  - definition.function: const singleParamArrow = x => x * 2;: 1 个
  - definition.function: function greetUser(name = 'Guest', greeting = 'Hello') {: 1 个
  - definition.function: function privateFunction() {: 1 个
  - definition.function: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {: 1 个
  - definition.function: function regularFunction(param1, param2) {: 1 个
  - definition.function: function sumAll(...args) {: 1 个
  - definition.function: function* numberGenerator(max) {: 1 个

代码块信息 (11 个):
  块 1: 第 20-38 行 (19 行)
     20: function regularFunction(param1, param2) {
     21:     return param1 + param2;
     22: }
    ... (还有 16 行)

  块 2: 第 40-50 行 (11 行)
     40: const asyncArrowFunction = async (id) => {
     41:     const data = await fetchData(`${API_URL}/items/${id}`);
     42:     return data;
    ... (还有 8 行)

  块 3: 第 65-78 行 (14 行)
     65:     greet() {
     66:         return `Hello, I'm ${this.name}`;
     67:     },
    ... (还有 11 行)

  块 4: 第 89-101 行 (13 行)
     89:     makeSound() {
     90:         return 'Some generic animal sound';
     91:     }
    ... (还有 10 行)

  块 5: 第 104-131 行 (28 行)
    104:     set nickname(value) {
    105:         this._nickname = value;
    106:     }
    ... (还有 25 行)

  块 6: 第 116-130 行 (15 行)
    116:     makeSound() {
    117:         return 'Woof!';
    118:     }
    ... (还有 12 行)

  块 7: 第 134-150 行 (17 行)
    134: const functionExpression = function(x) {
    135:     return x * x;
    136: };
    ... (还有 14 行)

  块 8: 第 154-166 行 (13 行)
    154: const [first, second, ...rest] = [1, 2, 3, 4, 5];
    155: 
    156: // Template literals
    ... (还有 10 行)

  块 9: 第 169-197 行 (29 行)
    169: function greetUser(name = 'Guest', greeting = 'Hello') {
    170:     return `${greeting}, ${name}!`;
    171: }
    ... (还有 26 行)

  块 10: 第 199-219 行 (21 行)
    199:     return {
    200:         publicMethod() {
    201:             return privateFunction();
    ... (还有 18 行)

  块 11: 第 216-218 行 (3 行)
    216:     getValue() {
    217:         return this.value;
    218:     }


统计信息:
  覆盖率: 83.2%
  块中总行数: 183
  结构类型数: 17
