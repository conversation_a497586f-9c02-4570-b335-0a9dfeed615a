{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js", "filename": "sample.js", "content_length": 4612, "line_count": 220}, "parsing_results": {"key_structure_lines": {"20": "definition.function: function regularFunction(param1, param2) {", "25": "definition.function: const arrowFunction = (x, y) => x * y;", "26": "definition.function: const singleParamArrow = x => x * 2;", "27": "definition.function: const noParamArrow = () => 'no params';", "30": "definition.function: async function fetchData(url) {", "40": "definition.function: const asyncArrowFunction = async (id) => {", "46": "definition.function: function* numberGenerator(max) {", "65": "definition.method: greet() {", "70": "definition.method: get displayName() {", "75": "definition.method: set fullName(value) {", "82": "definition.class: class Animal {", "89": "definition.method: makeSound() {", "94": "definition.method: static getKingdom() {", "99": "definition.method: get description() {", "104": "definition.method: set nickname(value) {", "110": "definition.class: class Dog extends Animal {", "116": "definition.method: makeSound() {", "128": "definition.method: getPrivateData() {", "134": "definition.function: const functionExpression = function(x) {", "138": "definition.function: const namedFunctionExpression = function square(x) {", "148": "definition.function: const higherOrderFunction = (callback) => {", "164": "definition.function: function sumAll(...args) {", "169": "definition.function: function greetUser(name = 'Guest', greeting = 'Hello') {", "174": "definition.function: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {", "195": "definition.function: function privateFunction() {", "200": "definition.method: publicMethod() {", "204": "definition.method: get counter() {", "211": "definition.class: export default class DefaultExport {", "216": "definition.method: getValue() {"}, "key_structure_count": 29, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js", "start_line": 20, "end_line": 43, "content": "function regularFunction(param1, param2) {\n    return param1 + param2;\n}\n\n// Arrow functions\nconst arrowFunction = (x, y) => x * y;\nconst singleParamArrow = x => x * 2;\nconst noParamArrow = () => 'no params';\n\n// Async functions\nasync function fetchData(url) {\n    try {\n        const response = await axios.get(url);\n        return response.data;\n    } catch (error) {\n        console.error('Error fetching data:', error);\n        throw error;\n    }\n}\n\nconst asyncArrowFunction = async (id) => {\n    const data = await fetchData(`${API_URL}/items/${id}`);\n    return data;\n};", "line_count": 24}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js", "start_line": 46, "end_line": 79, "content": "function* numberGenerator(max) {\n    for (let i = 0; i < max; i++) {\n        yield i;\n    }\n}\n\nconst generatorArrow = function* (start, end) {\n    for (let i = start; i <= end; i++) {\n        yield i;\n    }\n};\n\n// Object literals\nconst person = {\n    name: '<PERSON>',\n    age: 30,\n    email: '<EMAIL>',\n    \n    // Method shorthand\n    greet() {\n        return `Hello, I'm ${this.name}`;\n    },\n    \n    // Getter\n    get displayName() {\n        return `${this.name} (${this.age})`;\n    },\n    \n    // Setter\n    set fullName(value) {\n        const [first, last] = value.split(' ');\n        this.name = `${first} ${last}`;\n    }\n};", "line_count": 34}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js", "start_line": 82, "end_line": 107, "content": "class Animal {\n    constructor(name, species) {\n        this.name = name;\n        this.species = species;\n    }\n    \n    // Instance method\n    makeSound() {\n        return 'Some generic animal sound';\n    }\n    \n    // Static method\n    static getKingdom() {\n        return 'Animalia';\n    }\n    \n    // Getter\n    get description() {\n        return `${this.name} is a ${this.species}`;\n    }\n    \n    // Setter\n    set nickname(value) {\n        this._nickname = value;\n    }\n}", "line_count": 26}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js", "start_line": 110, "end_line": 131, "content": "class Dog extends Animal {\n    constructor(name, breed) {\n        super(name, 'dog');\n        this.breed = breed;\n    }\n    \n    makeSound() {\n        return 'Woof!';\n    }\n    \n    // Private method (ES2022)\n    #privateMethod() {\n        return 'This is private';\n    }\n    \n    // Private field\n    #privateField = 'secret';\n    \n    getPrivateData() {\n        return this.#privateMethod() + ' ' + this.#privateField;\n    }\n}", "line_count": 22}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js", "start_line": 134, "end_line": 154, "content": "const functionExpression = function(x) {\n    return x * x;\n};\n\nconst namedFunctionExpression = function square(x) {\n    return x * x;\n};\n\n// IIFE (Immediately Invoked Function Expression)\n(function() {\n    console.log('IIFE executed');\n})();\n\n// Higher-order functions\nconst higherOrderFunction = (callback) => {\n    return (value) => callback(value * 2);\n};\n\n// Destructuring\nconst { name, age } = person;\nconst [first, second, ...rest] = [1, 2, 3, 4, 5];", "line_count": 21}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js", "start_line": 160, "end_line": 197, "content": "const numbers = [1, 2, 3];\nconst moreNumbers = [...numbers, 4, 5, 6];\n\n// Rest parameters\nfunction sumAll(...args) {\n    return args.reduce((sum, num) => sum + num, 0);\n}\n\n// Default parameters\nfunction greetUser(name = 'Guest', greeting = 'Hello') {\n    return `${greeting}, ${name}!`;\n}\n\n// Object destructuring with default values\nfunction processUser({ name = 'Unknown', age = 0, email = null } = {}) {\n    return { name, age, email };\n}\n\n// Array methods with callbacks\nconst processedNumbers = numbers\n    .map(x => x * 2)\n    .filter(x => x > 2)\n    .reduce((sum, x) => sum + x, 0);\n\n// Promise handling\nconst promiseExample = new Promise((resolve, reject) => {\n    setTimeout(() => {\n        resolve('Promise resolved');\n    }, 1000);\n});\n\n// Module pattern\nconst modulePattern = (function() {\n    let privateVariable = 0;\n    \n    function privateFunction() {\n        return privateVariable++;\n    }", "line_count": 38}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js", "start_line": 199, "end_line": 219, "content": "    return {\n        publicMethod() {\n            return privateFunction();\n        },\n        \n        get counter() {\n            return privateVariable;\n        }\n    };\n})();\n\n// Export default\nexport default class DefaultExport {\n    constructor(value) {\n        this.value = value;\n    }\n    \n    getValue() {\n        return this.value;\n    }\n}", "line_count": 21}], "chunk_count": 7}, "analysis": {"detected_structures": ["definition.method: static getKingdom() {", "definition.class: class Animal {", "definition.function: const functionExpression = function(x) {", "definition.function: const asyncArrowFunction = async (id) => {", "definition.method: set nickname(value) {", "definition.function: async function fetchData(url) {", "definition.method: get displayName() {", "definition.method: makeSound() {", "definition.function: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {", "definition.method: get counter() {", "definition.function: const higherOrderFunction = (callback) => {", "definition.function: const arrowFunction = (x, y) => x * y;", "definition.method: greet() {", "definition.function: function sumAll(...args) {", "definition.method: getValue() {", "definition.method: getPrivateData() {", "definition.function: const namedFunctionExpression = function square(x) {", "definition.method: set fullName(value) {", "definition.class: class Dog extends Animal {", "definition.function: const noParamArrow = () => 'no params';", "definition.function: function regularFunction(param1, param2) {", "definition.class: export default class DefaultExport {", "definition.function: const singleParamArrow = x => x * 2;", "definition.function: function greetUser(name = 'Guest', greeting = 'Hello') {", "definition.function: function privateFunction() {", "definition.method: publicMethod() {", "definition.method: get description() {", "definition.function: function* numberGenerator(max) {"], "structure_types_count": 28, "total_lines_in_chunks": 186, "coverage_percentage": 84.54545454545455}}