FileParser 解析结果报告 - KOTLIN
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt
  文件名: sample.kt
  内容长度: 11440 字符
  行数: 385

关键结构行 (65 个):
  第  12 行: definition.class: @Serializable
           内容: @Serializable
  第  28 行: definition.function: fun activate() {
           内容: fun activate() {
  第  33 行: definition.function: fun updateName(newName: String) {
           内容: fun updateName(newName: String) {
  第  39 行: definition.class: @Serializable
           内容: @Serializable
  第  47 行: definition.class: sealed class Result<out T> {
           内容: sealed class Result<out T> {
  第  48 行: definition.class: data class Success<T>(val data: T) : Result<T>()
           内容: data class Success<T>(val data: T) : Result<T>()
  第  49 行: definition.class: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()
           内容: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()
  第  53 行: definition.class: sealed class UserEvent {
           内容: sealed class UserEvent {
  第  54 行: definition.class: data class Created(val user: User) : UserEvent()
           内容: data class Created(val user: User) : UserEvent()
  第  55 行: definition.class: data class Updated(val user: User) : UserEvent()
           内容: data class Updated(val user: User) : UserEvent()
  第  56 行: definition.class: data class Deleted(val userId: Long) : UserEvent()
           内容: data class Deleted(val userId: Long) : UserEvent()
  第  57 行: definition.class: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()
           内容: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()
  第  61 行: definition.class: enum class UserStatus(val displayName: String) {
           内容: enum class UserStatus(val displayName: String) {
  第  68 行: definition.function: fun fromString(value: String): UserStatus? {
           内容: fun fromString(value: String): UserStatus? {
  第  74 行: definition.class: enum class LogLevel {
           内容: enum class LogLevel {
  第  79 行: definition.class: interface UserRepository {
           内容: interface UserRepository {
  第  80 行: definition.function: suspend fun findById(id: Long): User?
           内容: suspend fun findById(id: Long): User?
  第  81 行: definition.function: suspend fun save(user: User): Boolean
           内容: suspend fun save(user: User): Boolean
  第  82 行: definition.function: suspend fun delete(id: Long): Boolean
           内容: suspend fun delete(id: Long): Boolean
  第  83 行: definition.function: suspend fun findAll(): List<User>
           内容: suspend fun findAll(): List<User>
  第  84 行: definition.function: suspend fun findByStatus(status: UserStatus): List<User>
           内容: suspend fun findByStatus(status: UserStatus): List<User>
  第  87 行: definition.class: interface Logger {
           内容: interface Logger {
  第  88 行: definition.function: fun log(level: LogLevel, message: String, throwable: Throwable? = null)
           内容: fun log(level: LogLevel, message: String, throwable: Throwable? = null)
  第  89 行: definition.function: fun info(message: String) = log(LogLevel.INFO, message)
           内容: fun info(message: String) = log(LogLevel.INFO, message)
  第  90 行: definition.function: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)
           内容: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)
  第  91 行: definition.function: fun debug(message: String) = log(LogLevel.DEBUG, message)
           内容: fun debug(message: String) = log(LogLevel.DEBUG, message)
  第  94 行: definition.class: interface Validator<T> {
           内容: interface Validator<T> {
  第  95 行: definition.function: fun validate(item: T): List<String>
           内容: fun validate(item: T): List<String>
  第  96 行: definition.function: fun isValid(item: T): Boolean = validate(item).isEmpty()
           内容: fun isValid(item: T): Boolean = validate(item).isEmpty()
  第 100 行: definition.class: abstract class BaseRepository<T, ID> {
           内容: abstract class BaseRepository<T, ID> {
  第 101 行: definition.function: protected abstract suspend fun doSave(item: T): Boolean
           内容: protected abstract suspend fun doSave(item: T): Boolean
  第 102 行: definition.function: protected abstract suspend fun doFindById(id: ID): T?
           内容: protected abstract suspend fun doFindById(id: ID): T?
  第 103 行: definition.function: protected abstract suspend fun doDelete(id: ID): Boolean
           内容: protected abstract suspend fun doDelete(id: ID): Boolean
  第 105 行: definition.function: suspend fun save(item: T): Result<T> = try {
           内容: suspend fun save(item: T): Result<T> = try {
  第 117 行: definition.class: class InMemoryUserRepository : UserRepository {
           内容: class InMemoryUserRepository : UserRepository {
  第 120 行: definition.function: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {
           内容: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {
  第 124 行: definition.function: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {
           内容: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {
  第 129 行: definition.function: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {
           内容: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {
  第 133 行: definition.function: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {
           内容: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {
  第 137 行: definition.function: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {
           内容: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {
  第 142 行: definition.class: class UserValidator : Validator<User> {
           内容: class UserValidator : Validator<User> {
  第 143 行: definition.function: override fun validate(item: User): List<String> {
           内容: override fun validate(item: User): List<String> {
  第 159 行: definition.function: private fun isValidEmail(email: String): Boolean {
           内容: private fun isValidEmail(email: String): Boolean {
  第 165 行: definition.class: class ConsoleLogger : Logger {
           内容: class ConsoleLogger : Logger {
  第 166 行: definition.function: override fun log(level: LogLevel, message: String, throwable: Throwable?) {
           内容: override fun log(level: LogLevel, message: String, throwable: Throwable?) {
  第 173 行: definition.class: class UserService(
           内容: class UserService(
  第 180 行: definition.function: suspend fun createUser(name: String, email: String): Result<User> = try {
           内容: suspend fun createUser(name: String, email: String): Result<User> = try {
  第 206 行: definition.function: suspend fun activateUser(id: Long): Boolean = try {
           内容: suspend fun activateUser(id: Long): Boolean = try {
  第 224 行: definition.function: suspend fun getUserStats(): Map<UserStatus, Int> = try {
           内容: suspend fun getUserStats(): Map<UserStatus, Int> = try {
  第 232 行: definition.function: fun addEventListener(listener: (UserEvent) -> Unit) {
           内容: fun addEventListener(listener: (UserEvent) -> Unit) {
  第 236 行: definition.function: private fun notifyListeners(event: UserEvent) {
           内容: private fun notifyListeners(event: UserEvent) {
  第 240 行: definition.function: private fun generateUserId(): Long = System.currentTimeMillis()
           内容: private fun generateUserId(): Long = System.currentTimeMillis()
  第 244 行: definition.function: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)
           内容: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)
  第 246 行: definition.function: fun String.toUser(): User? = try {
           内容: fun String.toUser(): User? = try {
  第 252 行: definition.function: fun List<User>.activeUsers(): List<User> = filter { it.isActive }
           内容: fun List<User>.activeUsers(): List<User> = filter { it.isActive }
  第 254 行: definition.function: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }
           内容: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }
  第 257 行: definition.function: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {
           内容: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {
  第 264 行: definition.function: suspend fun <T> retryOperation(
           内容: suspend fun <T> retryOperation(
  第 281 行: definition.function: fun <T> List<T>.chunked(size: Int): List<List<T>> {
           内容: fun <T> List<T>.chunked(size: Int): List<List<T>> {
  第 293 行: definition.function: fun createSampleUser(): User = User(
           内容: fun createSampleUser(): User = User(
  第 299 行: definition.function: fun createUsers(count: Int): List<User> = (1..count).map { i ->
           内容: fun createUsers(count: Int): List<User> = (1..count).map { i ->
  第 315 行: definition.class: class ApiClient {
           内容: class ApiClient {
  第 319 行: definition.function: fun create(): ApiClient = ApiClient()
           内容: fun create(): ApiClient = ApiClient()
  第 321 行: definition.function: fun createWithTimeout(timeoutMs: Long): ApiClient {
           内容: fun createWithTimeout(timeoutMs: Long): ApiClient {
  第 329 行: definition.function: suspend fun main() {
           内容: suspend fun main() {

检测到的结构类型:
  - definition.class: @Serializable: 2 个
  - definition.class: abstract class BaseRepository<T, ID> {: 1 个
  - definition.class: class ApiClient {: 1 个
  - definition.class: class ConsoleLogger : Logger {: 1 个
  - definition.class: class InMemoryUserRepository : UserRepository {: 1 个
  - definition.class: class UserService(: 1 个
  - definition.class: class UserValidator : Validator<User> {: 1 个
  - definition.class: data class Created(val user: User) : UserEvent(): 1 个
  - definition.class: data class Deleted(val userId: Long) : UserEvent(): 1 个
  - definition.class: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>(): 1 个
  - definition.class: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent(): 1 个
  - definition.class: data class Success<T>(val data: T) : Result<T>(): 1 个
  - definition.class: data class Updated(val user: User) : UserEvent(): 1 个
  - definition.class: enum class LogLevel {: 1 个
  - definition.class: enum class UserStatus(val displayName: String) {: 1 个
  - definition.class: interface Logger {: 1 个
  - definition.class: interface UserRepository {: 1 个
  - definition.class: interface Validator<T> {: 1 个
  - definition.class: sealed class Result<out T> {: 1 个
  - definition.class: sealed class UserEvent {: 1 个
  - definition.function: fun <T> List<T>.chunked(size: Int): List<List<T>> {: 1 个
  - definition.function: fun List<User>.activeUsers(): List<User> = filter { it.isActive }: 1 个
  - definition.function: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }: 1 个
  - definition.function: fun String.toUser(): User? = try {: 1 个
  - definition.function: fun User.toJson(): String = Json.encodeToString(User.serializer(), this): 1 个
  - definition.function: fun activate() {: 1 个
  - definition.function: fun addEventListener(listener: (UserEvent) -> Unit) {: 1 个
  - definition.function: fun create(): ApiClient = ApiClient(): 1 个
  - definition.function: fun createSampleUser(): User = User(: 1 个
  - definition.function: fun createUsers(count: Int): List<User> = (1..count).map { i ->: 1 个
  - definition.function: fun createWithTimeout(timeoutMs: Long): ApiClient {: 1 个
  - definition.function: fun debug(message: String) = log(LogLevel.DEBUG, message): 1 个
  - definition.function: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable): 1 个
  - definition.function: fun fromString(value: String): UserStatus? {: 1 个
  - definition.function: fun info(message: String) = log(LogLevel.INFO, message): 1 个
  - definition.function: fun isValid(item: T): Boolean = validate(item).isEmpty(): 1 个
  - definition.function: fun log(level: LogLevel, message: String, throwable: Throwable? = null): 1 个
  - definition.function: fun updateName(newName: String) {: 1 个
  - definition.function: fun validate(item: T): List<String>: 1 个
  - definition.function: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {: 1 个
  - definition.function: override fun log(level: LogLevel, message: String, throwable: Throwable?) {: 1 个
  - definition.function: override fun validate(item: User): List<String> {: 1 个
  - definition.function: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {: 1 个
  - definition.function: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {: 1 个
  - definition.function: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {: 1 个
  - definition.function: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {: 1 个
  - definition.function: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {: 1 个
  - definition.function: private fun generateUserId(): Long = System.currentTimeMillis(): 1 个
  - definition.function: private fun isValidEmail(email: String): Boolean {: 1 个
  - definition.function: private fun notifyListeners(event: UserEvent) {: 1 个
  - definition.function: protected abstract suspend fun doDelete(id: ID): Boolean: 1 个
  - definition.function: protected abstract suspend fun doFindById(id: ID): T?: 1 个
  - definition.function: protected abstract suspend fun doSave(item: T): Boolean: 1 个
  - definition.function: suspend fun <T> retryOperation(: 1 个
  - definition.function: suspend fun activateUser(id: Long): Boolean = try {: 1 个
  - definition.function: suspend fun createUser(name: String, email: String): Result<User> = try {: 1 个
  - definition.function: suspend fun delete(id: Long): Boolean: 1 个
  - definition.function: suspend fun findAll(): List<User>: 1 个
  - definition.function: suspend fun findById(id: Long): User?: 1 个
  - definition.function: suspend fun findByStatus(status: UserStatus): List<User>: 1 个
  - definition.function: suspend fun getUserStats(): Map<UserStatus, Int> = try {: 1 个
  - definition.function: suspend fun main() {: 1 个
  - definition.function: suspend fun save(item: T): Result<T> = try {: 1 个
  - definition.function: suspend fun save(user: User): Boolean: 1 个

代码块信息 (9 个):
  块 1: 第 12-37 行 (26 行)
     12: @Serializable
     13: data class User(
     14:     val id: Long,
    ... (还有 23 行)

  块 2: 第 39-72 行 (34 行)
     39: @Serializable
     40: data class UserStats(
     41:     val posts: Int = 0,
    ... (还有 31 行)

  块 3: 第 74-97 行 (24 行)
     74: enum class LogLevel {
     75:     DEBUG, INFO, WARNING, ERROR
     76: }
    ... (还有 21 行)

  块 4: 第 100-140 行 (41 行)
    100: abstract class BaseRepository<T, ID> {
    101:     protected abstract suspend fun doSave(item: T): Boolean
    102:     protected abstract suspend fun doFindById(id: ID): T?
    ... (还有 38 行)

  块 5: 第 142-163 行 (22 行)
    142: class UserValidator : Validator<User> {
    143:     override fun validate(item: User): List<String> {
    144:         val errors = mutableListOf<String>()
    ... (还有 19 行)

  块 6: 第 165-241 行 (77 行)
    165: class ConsoleLogger : Logger {
    166:     override fun log(level: LogLevel, message: String, throwable: Throwable?) {
    167:         val timestamp = LocalDateTime.now()
    ... (还有 74 行)

  块 7: 第 244-278 行 (35 行)
    244: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)
    245: 
    246: fun String.toUser(): User? = try {
    ... (还有 32 行)

  块 8: 第 281-306 行 (26 行)
    281: fun <T> List<T>.chunked(size: Int): List<List<T>> {
    282:     return if (size <= 0) {
    283:         throw IllegalArgumentException("Size must be positive")
    ... (还有 23 行)

  块 9: 第 308-384 行 (77 行)
    308: object Constants {
    309:     const val MAX_USERS = 1000
    310:     const val DEFAULT_PAGE_SIZE = 20
    ... (还有 74 行)


统计信息:
  覆盖率: 94.0%
  块中总行数: 362
  结构类型数: 64
