FileParser 解析结果报告 - KOTLIN
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt
  文件名: sample.kt
  内容长度: 11440 字符
  行数: 385

关键结构行 (67 个):
  第  12 行: definition.class: @Serializable
           内容: @Serializable
  第  13 行: name.definition.class: data class User(
           内容: data class User(
  第  28 行: name.definition.function: fun activate() {
           内容: fun activate() {
  第  33 行: name.definition.function: fun updateName(newName: String) {
           内容: fun updateName(newName: String) {
  第  39 行: definition.class: @Serializable
           内容: @Serializable
  第  40 行: name.definition.class: data class UserStats(
           内容: data class UserStats(
  第  47 行: name.definition.class: sealed class Result<out T> {
           内容: sealed class Result<out T> {
  第  48 行: name.definition.class: data class Success<T>(val data: T) : Result<T>()
           内容: data class Success<T>(val data: T) : Result<T>()
  第  49 行: name.definition.class: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()
           内容: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()
  第  53 行: name.definition.class: sealed class UserEvent {
           内容: sealed class UserEvent {
  第  54 行: name.definition.class: data class Created(val user: User) : UserEvent()
           内容: data class Created(val user: User) : UserEvent()
  第  55 行: name.definition.class: data class Updated(val user: User) : UserEvent()
           内容: data class Updated(val user: User) : UserEvent()
  第  56 行: name.definition.class: data class Deleted(val userId: Long) : UserEvent()
           内容: data class Deleted(val userId: Long) : UserEvent()
  第  57 行: name.definition.class: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()
           内容: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()
  第  61 行: name.definition.class: enum class UserStatus(val displayName: String) {
           内容: enum class UserStatus(val displayName: String) {
  第  68 行: name.definition.function: fun fromString(value: String): UserStatus? {
           内容: fun fromString(value: String): UserStatus? {
  第  74 行: name.definition.class: enum class LogLevel {
           内容: enum class LogLevel {
  第  79 行: name.definition.class: interface UserRepository {
           内容: interface UserRepository {
  第  80 行: name.definition.function: suspend fun findById(id: Long): User?
           内容: suspend fun findById(id: Long): User?
  第  81 行: name.definition.function: suspend fun save(user: User): Boolean
           内容: suspend fun save(user: User): Boolean
  第  82 行: name.definition.function: suspend fun delete(id: Long): Boolean
           内容: suspend fun delete(id: Long): Boolean
  第  83 行: name.definition.function: suspend fun findAll(): List<User>
           内容: suspend fun findAll(): List<User>
  第  84 行: name.definition.function: suspend fun findByStatus(status: UserStatus): List<User>
           内容: suspend fun findByStatus(status: UserStatus): List<User>
  第  87 行: name.definition.class: interface Logger {
           内容: interface Logger {
  第  88 行: name.definition.function: fun log(level: LogLevel, message: String, throwable: Throwable? = null)
           内容: fun log(level: LogLevel, message: String, throwable: Throwable? = null)
  第  89 行: name.definition.function: fun info(message: String) = log(LogLevel.INFO, message)
           内容: fun info(message: String) = log(LogLevel.INFO, message)
  第  90 行: name.definition.function: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)
           内容: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)
  第  91 行: name.definition.function: fun debug(message: String) = log(LogLevel.DEBUG, message)
           内容: fun debug(message: String) = log(LogLevel.DEBUG, message)
  第  94 行: name.definition.class: interface Validator<T> {
           内容: interface Validator<T> {
  第  95 行: name.definition.function: fun validate(item: T): List<String>
           内容: fun validate(item: T): List<String>
  第  96 行: name.definition.function: fun isValid(item: T): Boolean = validate(item).isEmpty()
           内容: fun isValid(item: T): Boolean = validate(item).isEmpty()
  第 100 行: name.definition.class: abstract class BaseRepository<T, ID> {
           内容: abstract class BaseRepository<T, ID> {
  第 101 行: name.definition.function: protected abstract suspend fun doSave(item: T): Boolean
           内容: protected abstract suspend fun doSave(item: T): Boolean
  第 102 行: name.definition.function: protected abstract suspend fun doFindById(id: ID): T?
           内容: protected abstract suspend fun doFindById(id: ID): T?
  第 103 行: name.definition.function: protected abstract suspend fun doDelete(id: ID): Boolean
           内容: protected abstract suspend fun doDelete(id: ID): Boolean
  第 105 行: name.definition.function: suspend fun save(item: T): Result<T> = try {
           内容: suspend fun save(item: T): Result<T> = try {
  第 117 行: name.definition.class: class InMemoryUserRepository : UserRepository {
           内容: class InMemoryUserRepository : UserRepository {
  第 120 行: name.definition.function: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {
           内容: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {
  第 124 行: name.definition.function: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {
           内容: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {
  第 129 行: name.definition.function: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {
           内容: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {
  第 133 行: name.definition.function: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {
           内容: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {
  第 137 行: name.definition.function: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {
           内容: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {
  第 142 行: name.definition.class: class UserValidator : Validator<User> {
           内容: class UserValidator : Validator<User> {
  第 143 行: name.definition.function: override fun validate(item: User): List<String> {
           内容: override fun validate(item: User): List<String> {
  第 159 行: name.definition.function: private fun isValidEmail(email: String): Boolean {
           内容: private fun isValidEmail(email: String): Boolean {
  第 165 行: name.definition.class: class ConsoleLogger : Logger {
           内容: class ConsoleLogger : Logger {
  第 166 行: name.definition.function: override fun log(level: LogLevel, message: String, throwable: Throwable?) {
           内容: override fun log(level: LogLevel, message: String, throwable: Throwable?) {
  第 173 行: name.definition.class: class UserService(
           内容: class UserService(
  第 180 行: name.definition.function: suspend fun createUser(name: String, email: String): Result<User> = try {
           内容: suspend fun createUser(name: String, email: String): Result<User> = try {
  第 206 行: name.definition.function: suspend fun activateUser(id: Long): Boolean = try {
           内容: suspend fun activateUser(id: Long): Boolean = try {
  第 224 行: name.definition.function: suspend fun getUserStats(): Map<UserStatus, Int> = try {
           内容: suspend fun getUserStats(): Map<UserStatus, Int> = try {
  第 232 行: name.definition.function: fun addEventListener(listener: (UserEvent) -> Unit) {
           内容: fun addEventListener(listener: (UserEvent) -> Unit) {
  第 236 行: name.definition.function: private fun notifyListeners(event: UserEvent) {
           内容: private fun notifyListeners(event: UserEvent) {
  第 240 行: name.definition.function: private fun generateUserId(): Long = System.currentTimeMillis()
           内容: private fun generateUserId(): Long = System.currentTimeMillis()
  第 244 行: name.definition.function: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)
           内容: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)
  第 246 行: name.definition.function: fun String.toUser(): User? = try {
           内容: fun String.toUser(): User? = try {
  第 252 行: name.definition.function: fun List<User>.activeUsers(): List<User> = filter { it.isActive }
           内容: fun List<User>.activeUsers(): List<User> = filter { it.isActive }
  第 254 行: name.definition.function: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }
           内容: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }
  第 257 行: name.definition.function: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {
           内容: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {
  第 264 行: name.definition.function: suspend fun <T> retryOperation(
           内容: suspend fun <T> retryOperation(
  第 281 行: name.definition.function: fun <T> List<T>.chunked(size: Int): List<List<T>> {
           内容: fun <T> List<T>.chunked(size: Int): List<List<T>> {
  第 293 行: name.definition.function: fun createSampleUser(): User = User(
           内容: fun createSampleUser(): User = User(
  第 299 行: name.definition.function: fun createUsers(count: Int): List<User> = (1..count).map { i ->
           内容: fun createUsers(count: Int): List<User> = (1..count).map { i ->
  第 315 行: name.definition.class: class ApiClient {
           内容: class ApiClient {
  第 319 行: name.definition.function: fun create(): ApiClient = ApiClient()
           内容: fun create(): ApiClient = ApiClient()
  第 321 行: name.definition.function: fun createWithTimeout(timeoutMs: Long): ApiClient {
           内容: fun createWithTimeout(timeoutMs: Long): ApiClient {
  第 329 行: name.definition.function: suspend fun main() {
           内容: suspend fun main() {

检测到的结构类型:
  - definition.class: @Serializable: 2 个
  - name.definition.class: abstract class BaseRepository<T, ID> {: 1 个
  - name.definition.class: class ApiClient {: 1 个
  - name.definition.class: class ConsoleLogger : Logger {: 1 个
  - name.definition.class: class InMemoryUserRepository : UserRepository {: 1 个
  - name.definition.class: class UserService(: 1 个
  - name.definition.class: class UserValidator : Validator<User> {: 1 个
  - name.definition.class: data class Created(val user: User) : UserEvent(): 1 个
  - name.definition.class: data class Deleted(val userId: Long) : UserEvent(): 1 个
  - name.definition.class: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>(): 1 个
  - name.definition.class: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent(): 1 个
  - name.definition.class: data class Success<T>(val data: T) : Result<T>(): 1 个
  - name.definition.class: data class Updated(val user: User) : UserEvent(): 1 个
  - name.definition.class: data class User(: 1 个
  - name.definition.class: data class UserStats(: 1 个
  - name.definition.class: enum class LogLevel {: 1 个
  - name.definition.class: enum class UserStatus(val displayName: String) {: 1 个
  - name.definition.class: interface Logger {: 1 个
  - name.definition.class: interface UserRepository {: 1 个
  - name.definition.class: interface Validator<T> {: 1 个
  - name.definition.class: sealed class Result<out T> {: 1 个
  - name.definition.class: sealed class UserEvent {: 1 个
  - name.definition.function: fun <T> List<T>.chunked(size: Int): List<List<T>> {: 1 个
  - name.definition.function: fun List<User>.activeUsers(): List<User> = filter { it.isActive }: 1 个
  - name.definition.function: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }: 1 个
  - name.definition.function: fun String.toUser(): User? = try {: 1 个
  - name.definition.function: fun User.toJson(): String = Json.encodeToString(User.serializer(), this): 1 个
  - name.definition.function: fun activate() {: 1 个
  - name.definition.function: fun addEventListener(listener: (UserEvent) -> Unit) {: 1 个
  - name.definition.function: fun create(): ApiClient = ApiClient(): 1 个
  - name.definition.function: fun createSampleUser(): User = User(: 1 个
  - name.definition.function: fun createUsers(count: Int): List<User> = (1..count).map { i ->: 1 个
  - name.definition.function: fun createWithTimeout(timeoutMs: Long): ApiClient {: 1 个
  - name.definition.function: fun debug(message: String) = log(LogLevel.DEBUG, message): 1 个
  - name.definition.function: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable): 1 个
  - name.definition.function: fun fromString(value: String): UserStatus? {: 1 个
  - name.definition.function: fun info(message: String) = log(LogLevel.INFO, message): 1 个
  - name.definition.function: fun isValid(item: T): Boolean = validate(item).isEmpty(): 1 个
  - name.definition.function: fun log(level: LogLevel, message: String, throwable: Throwable? = null): 1 个
  - name.definition.function: fun updateName(newName: String) {: 1 个
  - name.definition.function: fun validate(item: T): List<String>: 1 个
  - name.definition.function: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {: 1 个
  - name.definition.function: override fun log(level: LogLevel, message: String, throwable: Throwable?) {: 1 个
  - name.definition.function: override fun validate(item: User): List<String> {: 1 个
  - name.definition.function: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {: 1 个
  - name.definition.function: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {: 1 个
  - name.definition.function: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {: 1 个
  - name.definition.function: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {: 1 个
  - name.definition.function: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {: 1 个
  - name.definition.function: private fun generateUserId(): Long = System.currentTimeMillis(): 1 个
  - name.definition.function: private fun isValidEmail(email: String): Boolean {: 1 个
  - name.definition.function: private fun notifyListeners(event: UserEvent) {: 1 个
  - name.definition.function: protected abstract suspend fun doDelete(id: ID): Boolean: 1 个
  - name.definition.function: protected abstract suspend fun doFindById(id: ID): T?: 1 个
  - name.definition.function: protected abstract suspend fun doSave(item: T): Boolean: 1 个
  - name.definition.function: suspend fun <T> retryOperation(: 1 个
  - name.definition.function: suspend fun activateUser(id: Long): Boolean = try {: 1 个
  - name.definition.function: suspend fun createUser(name: String, email: String): Result<User> = try {: 1 个
  - name.definition.function: suspend fun delete(id: Long): Boolean: 1 个
  - name.definition.function: suspend fun findAll(): List<User>: 1 个
  - name.definition.function: suspend fun findById(id: Long): User?: 1 个
  - name.definition.function: suspend fun findByStatus(status: UserStatus): List<User>: 1 个
  - name.definition.function: suspend fun getUserStats(): Map<UserStatus, Int> = try {: 1 个
  - name.definition.function: suspend fun main() {: 1 个
  - name.definition.function: suspend fun save(item: T): Result<T> = try {: 1 个
  - name.definition.function: suspend fun save(user: User): Boolean: 1 个

代码块信息 (38 个):
  块 1: 第 13-25 行 (13 行)
     13: data class User(
     14:     val id: Long,
     15:     var name: String,
    ... (还有 10 行)

  块 2: 第 25-36 行 (12 行)
     25:     val displayName: String
     26:         get() = if (name.isBlank()) "Unknown User" else name
     27:     
    ... (还有 9 行)

  块 3: 第 33-44 行 (12 行)
     33:     fun updateName(newName: String) {
     34:         name = newName
     35:         updatedAt = LocalDateTime.now()
    ... (还有 9 行)

  块 4: 第 40-51 行 (12 行)
     40: data class UserStats(
     41:     val posts: Int = 0,
     42:     val followers: Int = 0,
    ... (还有 9 行)

  块 5: 第 47-58 行 (12 行)
     47: sealed class Result<out T> {
     48:     data class Success<T>(val data: T) : Result<T>()
     49:     data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()
    ... (还有 9 行)

  块 6: 第 53-72 行 (20 行)
     53: sealed class UserEvent {
     54:     data class Created(val user: User) : UserEvent()
     55:     data class Updated(val user: User) : UserEvent()
    ... (还有 17 行)

  块 7: 第 61-71 行 (11 行)
     61: enum class UserStatus(val displayName: String) {
     62:     ACTIVE("Active"),
     63:     INACTIVE("Inactive"),
    ... (还有 8 行)

  块 8: 第 68-85 行 (18 行)
     68:         fun fromString(value: String): UserStatus? {
     69:             return values().find { it.name.equals(value, ignoreCase = true) }
     70:         }
    ... (还有 15 行)

  块 9: 第 79-92 行 (14 行)
     79: interface UserRepository {
     80:     suspend fun findById(id: Long): User?
     81:     suspend fun save(user: User): Boolean
    ... (还有 11 行)

  块 10: 第 87-97 行 (11 行)
     87: interface Logger {
     88:     fun log(level: LogLevel, message: String, throwable: Throwable? = null)
     89:     fun info(message: String) = log(LogLevel.INFO, message)
    ... (还有 8 行)

  块 11: 第 94-114 行 (21 行)
     94: interface Validator<T> {
     95:     fun validate(item: T): List<String>
     96:     fun isValid(item: T): Boolean = validate(item).isEmpty()
    ... (还有 18 行)

  块 12: 第 100-113 行 (14 行)
    100: abstract class BaseRepository<T, ID> {
    101:     protected abstract suspend fun doSave(item: T): Boolean
    102:     protected abstract suspend fun doFindById(id: ID): T?
    ... (还有 11 行)

  块 13: 第 105-140 行 (36 行)
    105:     suspend fun save(item: T): Result<T> = try {
    106:         if (doSave(item)) {
    107:             Result.Success(item)
    ... (还有 33 行)

  块 14: 第 117-127 行 (11 行)
    117: class InMemoryUserRepository : UserRepository {
    118:     private val users = ConcurrentHashMap<Long, User>()
    119:     
    ... (还有 8 行)

  块 15: 第 124-135 行 (12 行)
    124:     override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {
    125:         users[user.id] = user
    126:         true
    ... (还有 9 行)

  块 16: 第 133-163 行 (31 行)
    133:     override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {
    134:         users.values.toList()
    135:     }
    ... (还有 28 行)

  块 17: 第 142-157 行 (16 行)
    142: class UserValidator : Validator<User> {
    143:     override fun validate(item: User): List<String> {
    144:         val errors = mutableListOf<String>()
    ... (还有 13 行)

  块 18: 第 143-162 行 (20 行)
    143:     override fun validate(item: User): List<String> {
    144:         val errors = mutableListOf<String>()
    145:         
    ... (还有 17 行)

  块 19: 第 159-171 行 (13 行)
    159:     private fun isValidEmail(email: String): Boolean {
    160:         val emailRegex = Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")
    161:         return emailRegex.matches(email)
    ... (还有 10 行)

  块 20: 第 165-165 行 (1 行)
    165: class ConsoleLogger : Logger {

  块 21: 第 173-204 行 (32 行)
    173: class UserService(
    174:     private val repository: UserRepository,
    175:     private val validator: Validator<User>,
    ... (还有 29 行)

  块 22: 第 180-222 行 (43 行)
    180:     suspend fun createUser(name: String, email: String): Result<User> = try {
    181:         val user = User(
    182:             id = generateUserId(),
    ... (还有 40 行)

  块 23: 第 206-230 行 (25 行)
    206:     suspend fun activateUser(id: Long): Boolean = try {
    207:         val user = repository.findById(id)
    208:         if (user != null) {
    ... (还有 22 行)

  块 24: 第 224-234 行 (11 行)
    224:     suspend fun getUserStats(): Map<UserStatus, Int> = try {
    225:         val users = repository.findAll()
    226:         users.groupingBy { it.status }.eachCount()
    ... (还有 8 行)

  块 25: 第 232-244 行 (13 行)
    232:     fun addEventListener(listener: (UserEvent) -> Unit) {
    233:         eventListeners.add(listener)
    234:     }
    ... (还有 10 行)

  块 26: 第 244-254 行 (11 行)
    244: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)
    245: 
    246: fun String.toUser(): User? = try {
    ... (还有 8 行)

  块 27: 第 254-278 行 (25 行)
    254: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }
    255: 
    256: // Higher-order functions
    ... (还有 22 行)

  块 28: 第 264-289 行 (26 行)
    264: suspend fun <T> retryOperation(
    265:     times: Int = 3,
    266:     delay: Long = 1000,
    ... (还有 23 行)

  块 29: 第 281-306 行 (26 行)
    281: fun <T> List<T>.chunked(size: Int): List<List<T>> {
    282:     return if (size <= 0) {
    283:         throw IllegalArgumentException("Size must be positive")
    ... (还有 23 行)

  块 30: 第 292-305 行 (14 行)
    292: object UserFactory {
    293:     fun createSampleUser(): User = User(
    294:         id = 1L,
    ... (还有 11 行)

  块 31: 第 299-312 行 (14 行)
    299:     fun createUsers(count: Int): List<User> = (1..count).map { i ->
    300:         User(
    301:             id = i.toLong(),
    ... (还有 11 行)

  块 32: 第 308-326 行 (19 行)
    308: object Constants {
    309:     const val MAX_USERS = 1000
    310:     const val DEFAULT_PAGE_SIZE = 20
    ... (还有 16 行)

  块 33: 第 315-325 行 (11 行)
    315: class ApiClient {
    316:     companion object {
    317:         private const val BASE_URL = "https://api.example.com"
    ... (还有 8 行)

  块 34: 第 317-317 行 (1 行)
    317:         private const val BASE_URL = "https://api.example.com"

  块 35: 第 329-356 行 (28 行)
    329: suspend fun main() {
    330:     val logger = ConsoleLogger()
    331:     val repository = InMemoryUserRepository()
    ... (还有 25 行)

  块 36: 第 356-366 行 (11 行)
    356:             val activated = service.activateUser(user1Result.data.id)
    357:             if (activated) {
    358:                 logger.info("User activated successfully")
    ... (还有 8 行)

  块 37: 第 366-376 行 (11 行)
    366:     val sampleUsers = UserFactory.createUsers(5)
    367:     sampleUsers.forEach { user ->
    368:         repository.save(user)
    ... (还有 8 行)

  块 38: 第 376-376 行 (1 行)
    376:     val allUsers = repository.findAll()


统计信息:
  覆盖率: 164.2%
  块中总行数: 632
  结构类型数: 66
