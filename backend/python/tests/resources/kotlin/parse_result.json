{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "filename": "sample.kt", "content_length": 11440, "line_count": 385}, "parsing_results": {"key_structure_lines": {"12": "definition.class: @Serializable", "28": "definition.function: fun activate() {", "33": "definition.function: fun updateName(newName: String) {", "39": "definition.class: @Serializable", "47": "definition.class: sealed class Result<out T> {", "48": "definition.class: data class Success<T>(val data: T) : Result<T>()", "49": "definition.class: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()", "53": "definition.class: sealed class UserEvent {", "54": "definition.class: data class Created(val user: User) : UserEvent()", "55": "definition.class: data class Updated(val user: User) : UserEvent()", "56": "definition.class: data class Deleted(val userId: Long) : UserEvent()", "57": "definition.class: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()", "61": "definition.class: enum class UserStatus(val displayName: String) {", "68": "definition.function: fun fromString(value: String): UserStatus? {", "74": "definition.class: enum class LogLevel {", "79": "definition.class: interface UserRepository {", "80": "definition.function: suspend fun findById(id: Long): User?", "81": "definition.function: suspend fun save(user: User): <PERSON><PERSON><PERSON>", "82": "definition.function: suspend fun delete(id: Long): <PERSON><PERSON><PERSON>", "83": "definition.function: suspend fun findAll(): List<User>", "84": "definition.function: suspend fun findByStatus(status: UserStatus): List<User>", "87": "definition.class: interface Logger {", "88": "definition.function: fun log(level: LogLevel, message: String, throwable: Throwable? = null)", "89": "definition.function: fun info(message: String) = log(LogLevel.INFO, message)", "90": "definition.function: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)", "91": "definition.function: fun debug(message: String) = log(LogLevel.DEBUG, message)", "94": "definition.class: interface Validator<T> {", "95": "definition.function: fun validate(item: T): List<String>", "96": "definition.function: fun isValid(item: T): Boolean = validate(item).isEmpty()", "100": "definition.class: abstract class BaseRepository<T, ID> {", "101": "definition.function: protected abstract suspend fun doSave(item: T): <PERSON><PERSON><PERSON>", "102": "definition.function: protected abstract suspend fun doFindById(id: ID): T?", "103": "definition.function: protected abstract suspend fun doDelete(id: ID): <PERSON><PERSON><PERSON>", "105": "definition.function: suspend fun save(item: T): Result<T> = try {", "117": "definition.class: class InMemoryUserRepository : UserRepository {", "120": "definition.function: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {", "124": "definition.function: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {", "129": "definition.function: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {", "133": "definition.function: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {", "137": "definition.function: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {", "142": "definition.class: class UserValidator : Validator<User> {", "143": "definition.function: override fun validate(item: User): List<String> {", "159": "definition.function: private fun isValidEmail(email: String): <PERSON><PERSON><PERSON> {", "165": "definition.class: class ConsoleLogger : Logger {", "166": "definition.function: override fun log(level: LogLevel, message: String, throwable: Throwable?) {", "173": "definition.class: class UserService(", "180": "definition.function: suspend fun createUser(name: String, email: String): Result<User> = try {", "206": "definition.function: suspend fun activateUser(id: Long): Boolean = try {", "224": "definition.function: suspend fun getUserStats(): Map<UserStatus, Int> = try {", "232": "definition.function: fun addEventListener(listener: (UserEvent) -> Unit) {", "236": "definition.function: private fun notifyListeners(event: UserEvent) {", "240": "definition.function: private fun generateUserId(): Long = System.currentTimeMillis()", "244": "definition.function: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)", "246": "definition.function: fun String.toUser(): User? = try {", "252": "definition.function: fun List<User>.activeUsers(): List<User> = filter { it.isActive }", "254": "definition.function: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }", "257": "definition.function: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {", "264": "definition.function: suspend fun <T> retryOperation(", "281": "definition.function: fun <T> List<T>.chunked(size: Int): List<List<T>> {", "293": "definition.function: fun createSampleUser(): User = User(", "299": "definition.function: fun createUsers(count: Int): List<User> = (1..count).map { i ->", "315": "definition.class: class ApiClient {", "319": "definition.function: fun create(): ApiClient = ApiClient()", "321": "definition.function: fun createWithTimeout(timeoutMs: Long): ApiClient {", "329": "definition.function: suspend fun main() {"}, "key_structure_count": 65, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 12, "end_line": 37, "content": "@Serializable\ndata class User(\n    val id: Long,\n    var name: String,\n    var email: String,\n    var status: UserStatus = UserStatus.PENDING,\n    var avatar: String? = null,\n    val createdAt: LocalDateTime = LocalDateTime.now(),\n    var updatedAt: LocalDateTime = LocalDateTime.now()\n) {\n    val isActive: Boolean\n        get() = status == UserStatus.ACTIVE\n    \n    val displayName: String\n        get() = if (name.isBlank()) \"Unknown User\" else name\n    \n    fun activate() {\n        status = UserStatus.ACTIVE\n        updatedAt = LocalDateTime.now()\n    }\n    \n    fun updateName(newName: String) {\n        name = newName\n        updatedAt = LocalDateTime.now()\n    }\n}", "line_count": 26}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 39, "end_line": 72, "content": "@Serializable\ndata class UserStats(\n    val posts: Int = 0,\n    val followers: Int = 0,\n    val following: Int = 0\n)\n\n// Sealed classes\nsealed class Result<out T> {\n    data class Success<T>(val data: T) : Result<T>()\n    data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()\n    object Loading : Result<Nothing>()\n}\n\nsealed class UserEvent {\n    data class Created(val user: User) : UserEvent()\n    data class Updated(val user: User) : UserEvent()\n    data class Deleted(val userId: Long) : UserEvent()\n    data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()\n}\n\n// Enums\nenum class UserStatus(val displayName: String) {\n    ACTIVE(\"Active\"),\n    INACTIVE(\"Inactive\"),\n    SUSPENDED(\"Suspended\"),\n    PENDING(\"Pending Verification\");\n    \n    companion object {\n        fun fromString(value: String): UserStatus? {\n            return values().find { it.name.equals(value, ignoreCase = true) }\n        }\n    }\n}", "line_count": 34}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 74, "end_line": 97, "content": "enum class LogLevel {\n    DEBUG, INFO, WARNING, ERROR\n}\n\n// Interfaces\ninterface UserRepository {\n    suspend fun findById(id: Long): User?\n    suspend fun save(user: User): Boolean\n    suspend fun delete(id: Long): Boolean\n    suspend fun findAll(): List<User>\n    suspend fun findByStatus(status: UserStatus): List<User>\n}\n\ninterface Logger {\n    fun log(level: LogLevel, message: String, throwable: Throwable? = null)\n    fun info(message: String) = log(LogLevel.INFO, message)\n    fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)\n    fun debug(message: String) = log(LogLevel.DEBUG, message)\n}\n\ninterface Validator<T> {\n    fun validate(item: T): List<String>\n    fun isValid(item: T): Boolean = validate(item).isEmpty()\n}", "line_count": 24}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 100, "end_line": 140, "content": "abstract class BaseRepository<T, ID> {\n    protected abstract suspend fun doSave(item: T): Boolean\n    protected abstract suspend fun doFindById(id: ID): T?\n    protected abstract suspend fun doDelete(id: ID): Boolean\n    \n    suspend fun save(item: T): Result<T> = try {\n        if (doSave(item)) {\n            Result.Success(item)\n        } else {\n            Result.Error(\"Failed to save item\")\n        }\n    } catch (e: Exception) {\n        Result.Error(\"Save operation failed\", e)\n    }\n}\n\n// Classes\nclass InMemoryUserRepository : UserRepository {\n    private val users = ConcurrentHashMap<Long, User>()\n    \n    override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {\n        users[id]\n    }\n    \n    override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {\n        users[user.id] = user\n        true\n    }\n    \n    override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {\n        users.remove(id) != null\n    }\n    \n    override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {\n        users.values.toList()\n    }\n    \n    override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {\n        users.values.filter { it.status == status }\n    }\n}", "line_count": 41}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 142, "end_line": 163, "content": "class UserValidator : Validator<User> {\n    override fun validate(item: User): List<String> {\n        val errors = mutableListOf<String>()\n        \n        if (item.name.isBlank()) {\n            errors.add(\"Name is required\")\n        }\n        \n        if (item.email.isBlank()) {\n            errors.add(\"Email is required\")\n        } else if (!isValidEmail(item.email)) {\n            errors.add(\"Invalid email format\")\n        }\n        \n        return errors\n    }\n    \n    private fun isValidEmail(email: String): <PERSON><PERSON><PERSON> {\n        val emailRegex = Regex(\"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}\")\n        return emailRegex.matches(email)\n    }\n}", "line_count": 22}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 165, "end_line": 241, "content": "class ConsoleLogger : Logger {\n    override fun log(level: LogLevel, message: String, throwable: Throwable?) {\n        val timestamp = LocalDateTime.now()\n        println(\"[$timestamp] [$level] $message\")\n        throwable?.printStackTrace()\n    }\n}\n\nclass UserService(\n    private val repository: UserRepository,\n    private val validator: Validator<User>,\n    private val logger: Logger\n) {\n    private val eventListeners = mutableListOf<(UserEvent) -> Unit>()\n    \n    suspend fun createUser(name: String, email: String): Result<User> = try {\n        val user = User(\n            id = generateUserId(),\n            name = name,\n            email = email\n        )\n        \n        val validationErrors = validator.validate(user)\n        if (validationErrors.isNotEmpty()) {\n            logger.error(\"Invalid user data: ${validationErrors.joinToString(\", \")}\")\n            return Result.Error(\"Invalid user data: ${validationErrors.joinToString(\", \")}\")\n        }\n        \n        if (repository.save(user)) {\n            logger.info(\"User created successfully: ${user.id}\")\n            notifyListeners(UserEvent.Created(user))\n            Result.Success(user)\n        } else {\n            logger.error(\"Failed to save user\")\n            Result.Error(\"Failed to save user\")\n        }\n    } catch (e: Exception) {\n        logger.error(\"Error creating user\", e)\n        Result.Error(\"Error creating user: ${e.message}\", e)\n    }\n    \n    suspend fun activateUser(id: Long): Boolean = try {\n        val user = repository.findById(id)\n        if (user != null) {\n            user.activate()\n            val success = repository.save(user)\n            if (success) {\n                notifyListeners(UserEvent.StatusChanged(id, UserStatus.ACTIVE))\n            }\n            success\n        } else {\n            logger.error(\"User not found: $id\")\n            false\n        }\n    } catch (e: Exception) {\n        logger.error(\"Error activating user\", e)\n        false\n    }\n    \n    suspend fun getUserStats(): Map<UserStatus, Int> = try {\n        val users = repository.findAll()\n        users.groupingBy { it.status }.eachCount()\n    } catch (e: Exception) {\n        logger.error(\"Error getting user stats\", e)\n        emptyMap()\n    }\n    \n    fun addEventListener(listener: (UserEvent) -> Unit) {\n        eventListeners.add(listener)\n    }\n    \n    private fun notifyListeners(event: UserEvent) {\n        eventListeners.forEach { it(event) }\n    }\n    \n    private fun generateUserId(): Long = System.currentTimeMillis()\n}", "line_count": 77}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 244, "end_line": 278, "content": "fun User.toJson(): String = Json.encodeToString(User.serializer(), this)\n\nfun String.toUser(): User? = try {\n    Json.decodeFromString(User.serializer(), this)\n} catch (e: Exception) {\n    null\n}\n\nfun List<User>.activeUsers(): List<User> = filter { it.isActive }\n\nfun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }\n\n// Higher-order functions\ninline fun <T> measureTime(operation: () -> T): Pair<T, Long> {\n    val startTime = System.currentTimeMillis()\n    val result = operation()\n    val endTime = System.currentTimeMillis()\n    return result to (endTime - startTime)\n}\n\nsuspend fun <T> retryOperation(\n    times: Int = 3,\n    delay: Long = 1000,\n    operation: suspend () -> T\n): T? {\n    repeat(times) { attempt ->\n        try {\n            return operation()\n        } catch (e: Exception) {\n            if (attempt == times - 1) throw e\n            delay(delay)\n        }\n    }\n    return null\n}", "line_count": 35}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 281, "end_line": 306, "content": "fun <T> List<T>.chunked(size: Int): List<List<T>> {\n    return if (size <= 0) {\n        throw IllegalArgumentException(\"<PERSON><PERSON> must be positive\")\n    } else {\n        (0 until this.size step size).map { i ->\n            this.subList(i, minOf(i + size, this.size))\n        }\n    }\n}\n\n// Object declarations\nobject UserFactory {\n    fun createSampleUser(): User = User(\n        id = 1L,\n        name = \"<PERSON>\",\n        email = \"<EMAIL>\"\n    )\n    \n    fun createUsers(count: Int): List<User> = (1..count).map { i ->\n        User(\n            id = i.toLong(),\n            name = \"User $i\",\n            email = \"user$<EMAIL>\"\n        )\n    }\n}", "line_count": 26}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 308, "end_line": 384, "content": "object Constants {\n    const val MAX_USERS = 1000\n    const val DEFAULT_PAGE_SIZE = 20\n    const val API_VERSION = \"v1\"\n}\n\n// Companion objects\nclass ApiClient {\n    companion object {\n        private const val BASE_URL = \"https://api.example.com\"\n        \n        fun create(): ApiClient = ApiClient()\n        \n        fun createWithTimeout(timeoutMs: Long): ApiClient {\n            // Implementation would configure timeout\n            return ApiClient()\n        }\n    }\n}\n\n// Main function and example usage\nsuspend fun main() {\n    val logger = ConsoleLogger()\n    val repository = InMemoryUserRepository()\n    val validator = UserValidator()\n    val service = UserService(repository, validator, logger)\n    \n    // Add event listener\n    service.addEventListener { event ->\n        when (event) {\n            is UserEvent.Created -> logger.info(\"User created: ${event.user.name}\")\n            is UserEvent.Updated -> logger.info(\"User updated: ${event.user.name}\")\n            is UserEvent.Deleted -> logger.info(\"User deleted: ${event.userId}\")\n            is UserEvent.StatusChanged -> logger.info(\"User ${event.userId} status changed to ${event.newStatus}\")\n        }\n    }\n    \n    // Create users\n    val (user1Result, time1) = measureTime {\n        runBlocking { service.createUser(\"Alice\", \"<EMAIL>\") }\n    }\n    logger.info(\"User creation took ${time1}ms\")\n    \n    when (user1Result) {\n        is Result.Success -> {\n            logger.info(\"Created user: ${user1Result.data.name}\")\n            \n            // Activate user\n            val activated = service.activateUser(user1Result.data.id)\n            if (activated) {\n                logger.info(\"User activated successfully\")\n            }\n        }\n        is Result.Error -> logger.error(\"Failed to create user: ${user1Result.message}\")\n        is Result.Loading -> logger.info(\"Still loading...\")\n    }\n    \n    // Create multiple users\n    val sampleUsers = UserFactory.createUsers(5)\n    sampleUsers.forEach { user ->\n        repository.save(user)\n    }\n    \n    // Get stats\n    val stats = service.getUserStats()\n    logger.info(\"User statistics: $stats\")\n    \n    // Use extension functions\n    val allUsers = repository.findAll()\n    val activeUsers = allUsers.activeUsers()\n    logger.info(\"Active users: ${activeUsers.size}\")\n    \n    // Process users in chunks\n    allUsers.chunked(2).forEachIndexed { index, chunk ->\n        logger.info(\"Processing chunk $index with ${chunk.size} users\")\n    }\n}", "line_count": 77}], "chunk_count": 9}, "analysis": {"detected_structures": ["definition.class: interface Logger {", "definition.function: suspend fun save(item: T): Result<T> = try {", "definition.function: private fun generateUserId(): Long = System.currentTimeMillis()", "definition.class: @Serializable", "definition.function: fun createSampleUser(): User = User(", "definition.function: fun updateName(newName: String) {", "definition.class: sealed class Result<out T> {", "definition.class: data class Updated(val user: User) : UserEvent()", "definition.function: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {", "definition.function: protected abstract suspend fun doSave(item: T): <PERSON><PERSON><PERSON>", "definition.function: fun debug(message: String) = log(LogLevel.DEBUG, message)", "definition.function: override fun validate(item: User): List<String> {", "definition.class: data class Deleted(val userId: Long) : UserEvent()", "definition.function: override fun log(level: LogLevel, message: String, throwable: Throwable?) {", "definition.function: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {", "definition.function: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {", "definition.class: class UserValidator : Validator<User> {", "definition.function: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)", "definition.class: abstract class BaseRepository<T, ID> {", "definition.class: sealed class UserEvent {", "definition.function: suspend fun delete(id: Long): <PERSON><PERSON><PERSON>", "definition.function: private fun isValidEmail(email: String): <PERSON><PERSON><PERSON> {", "definition.function: fun create(): ApiClient = ApiClient()", "definition.function: fun createWithTimeout(timeoutMs: Long): ApiClient {", "definition.function: fun fromString(value: String): UserStatus? {", "definition.class: enum class UserStatus(val displayName: String) {", "definition.function: suspend fun findById(id: Long): User?", "definition.function: fun createUsers(count: Int): List<User> = (1..count).map { i ->", "definition.function: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)", "definition.class: class UserService(", "definition.class: interface Validator<T> {", "definition.function: suspend fun findAll(): List<User>", "definition.class: interface UserRepository {", "definition.class: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()", "definition.function: fun List<User>.activeUsers(): List<User> = filter { it.isActive }", "definition.function: suspend fun save(user: User): <PERSON><PERSON><PERSON>", "definition.function: private fun notifyListeners(event: UserEvent) {", "definition.function: fun <T> List<T>.chunked(size: Int): List<List<T>> {", "definition.function: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }", "definition.function: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {", "definition.class: data class Success<T>(val data: T) : Result<T>()", "definition.class: class InMemoryUserRepository : UserRepository {", "definition.function: fun log(level: LogLevel, message: String, throwable: Throwable? = null)", "definition.function: fun addEventListener(listener: (UserEvent) -> Unit) {", "definition.function: fun activate() {", "definition.function: protected abstract suspend fun doFindById(id: ID): T?", "definition.class: class ApiClient {", "definition.class: data class Created(val user: User) : UserEvent()", "definition.function: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {", "definition.function: suspend fun getUserStats(): Map<UserStatus, Int> = try {", "definition.function: fun info(message: String) = log(LogLevel.INFO, message)", "definition.function: fun isValid(item: T): Boolean = validate(item).isEmpty()", "definition.function: protected abstract suspend fun doDelete(id: ID): <PERSON><PERSON><PERSON>", "definition.function: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {", "definition.function: fun String.toUser(): User? = try {", "definition.function: suspend fun activateUser(id: Long): Boolean = try {", "definition.function: suspend fun <T> retryOperation(", "definition.function: suspend fun createUser(name: String, email: String): Result<User> = try {", "definition.function: suspend fun findByStatus(status: UserStatus): List<User>", "definition.function: fun validate(item: T): List<String>", "definition.class: enum class LogLevel {", "definition.class: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()", "definition.class: class ConsoleLogger : Logger {", "definition.function: suspend fun main() {"], "structure_types_count": 64, "total_lines_in_chunks": 362, "coverage_percentage": 94.02597402597402}}