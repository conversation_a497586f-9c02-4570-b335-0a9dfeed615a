{"generated_at": "2025-08-24T01:33:29.383961", "parser_config": {"min_chunk_size": 20, "max_chunk_size": 100, "overflow_size": 5}, "languages": {"python": {"success": true, "key_structures": 27, "chunks": 6, "structure_types": ["definition.function: def simple_function(x: int, y: int = 10) -> int:", "definition.function: def typed_function(", "definition.function: def __init__(self, name: str, breed: str):", "definition.function: def match_case_example(value):", "definition.class: class Person:", "definition.class: class Animal(ABC):", "definition.class: @dataclass", "definition.function: def function_with_context_managers():", "definition.function: def description(self) -> str:", "definition.function: @classmethod", "definition.function: def generator_function(n: int):", "definition.function: def __post_init__(self):", "definition.function: def make_sound(self) -> str:", "definition.function: def inner_function():", "definition.function: def is_good_boy() -> bool:", "definition.function: def function_with_global_nonlocal():", "definition.class: class Dog(Animal):", "definition.function: def function_with_exception_handling():", "definition.function: @staticmethod", "definition.function: @abstractmethod", "definition.function: def decorated_function():", "definition.function: def __init__(self, name: str, species: str):", "definition.function: def create_puppy(cls, name: str, breed: str) -> 'Dog':", "definition.function: @property", "definition.function: async def async_function(data: List[str]) -> Dict[str, int]:"], "coverage": 82.84023668639054}, "java": {"success": true, "key_structures": 32, "chunks": 5, "structure_types": ["definition.class: abstract class Shape {", "definition.interface: interface Drawable {", "definition.class: public class Sample {", "definition.enum: enum Color {", "definition.class: public class InnerClass {", "definition.method: @Override", "definition.method: public String getInnerField() {", "definition.constructor: public Circle(String color, double radius) {", "definition.constructor: public Sample() {", "definition.constructor: public Shape(String color) {", "definition.method: void draw();", "definition.method: public void publicMethod() {", "definition.method: public String getDescription() {", "definition.method: public <T> List<T> genericMethod(T item, int count) {", "definition.constructor: Color(String description) {", "definition.constructor: public InnerClass(String value) {", "name.definition.method: public void draw() {", "name.definition.method: public double getArea() {", "definition.method: static void staticInterfaceMethod() {", "definition.method: private int privateMethod(int a, int b) {", "definition.constructor: public Sample(int value, List<String> items) {", "definition.method: public String getColor() {", "definition.constructor: public Person(String name, int age) {", "definition.class: class Circle extends Shape implements Drawable {", "definition.method: public abstract double getArea();", "definition.class: public static class StaticNestedClass {", "definition.method: default void print() {", "definition.method: protected static String staticMethod(String input) {", "definition.method: public static void staticNestedMethod() {", "definition.method: public String getDisplayName() {", "definition.method: public void lambdaExamples() {"], "coverage": 97.77777777777777}, "javascript": {"success": true, "key_structures": 29, "chunks": 7, "structure_types": ["definition.method: static getKingdom() {", "definition.class: class Animal {", "definition.function: const functionExpression = function(x) {", "definition.function: const asyncArrowFunction = async (id) => {", "definition.method: set nickname(value) {", "definition.function: async function fetchData(url) {", "definition.method: get displayName() {", "definition.method: makeSound() {", "definition.function: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {", "definition.method: get counter() {", "definition.function: const higherOrderFunction = (callback) => {", "definition.function: const arrowFunction = (x, y) => x * y;", "definition.method: greet() {", "definition.function: function sumAll(...args) {", "definition.method: getValue() {", "definition.method: getPrivateData() {", "definition.function: const namedFunctionExpression = function square(x) {", "definition.method: set fullName(value) {", "definition.class: class Dog extends Animal {", "definition.function: const noParamArrow = () => 'no params';", "definition.function: function regularFunction(param1, param2) {", "definition.class: export default class DefaultExport {", "definition.function: const singleParamArrow = x => x * 2;", "definition.function: function greetUser(name = 'Guest', greeting = 'Hello') {", "definition.function: function privateFunction() {", "definition.method: publicMethod() {", "definition.method: get description() {", "definition.function: function* numberGenerator(max) {"], "coverage": 84.54545454545455}, "typescript": {"success": true, "key_structures": 40, "chunks": 8, "structure_types": ["definition.function: export function isValidEmail(email: string): boolean {", "definition.function: export function formatDate(date: Date): string {", "definition.class: abstract class Shape {", "definition.function: function isString(value: any): value is string {", "definition.interface: interface User {", "definition.method: getColor(): string {", "definition.function: function combine(a: number, b: number): number;", "definition.function: function processData<T>(", "definition.method: getArea(): number {", "definition.interface: interface Window {", "definition.method: async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {", "definition.class: class UserService implements Repository<User> {", "definition.interface: interface ApiResponse<T> {", "definition.function: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {", "definition.method: static log(message: string): void {", "definition.namespace: namespace Utils {", "definition.function: function isUser(obj: any): obj is User {", "definition.enum: enum Direction {", "definition.enum: enum Status {", "definition.function: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {", "definition.class: export class Logger {", "definition.method: getPerimeter(): number {", "definition.method: multiply(a: number, b: number): number {", "definition.method: async save(user: User): Promise<User> {", "definition.method: constructor(color: string, private radius: number) {", "definition.method: add(a: number, b: number): number {", "definition.function: function combine(a: string, b: string): string;", "definition.method: protected constructor(protected color: string) {}", "definition.class: class Calculator {", "definition.method: findById(id: number): Promise<T | null>;", "definition.method: async delete(id: number): Promise<void> {", "definition.method: constructor(private apiUrl: string) {}", "definition.method: abstract getPerimeter(): number;", "definition.function: function assertIsNumber(value: any): asserts value is number {", "definition.method: async findById(id: number): Promise<User | null> {", "definition.method: abstract getArea(): number;", "definition.method: save(entity: T): Promise<T>;", "definition.class: class Circle extends Shape {", "definition.method: delete(id: number): Promise<void>;", "definition.interface: interface Repository<T> {"], "coverage": 87.73584905660378}, "c": {"success": true, "key_structures": 21, "chunks": 6, "structure_types": ["definition.function: int subtract(int a, int b) {", "definition.function: int add(int a, int b);", "definition.function: void print_array(int arr[], int size);", "definition.function: void print_list(Node* head) {", "definition.function: int sum_all(int count, ...) {", "definition.function: void insert_node(Node** head, int data) {", "definition.function: static int get_next_id(void) {", "definition.function: int add(int a, int b) {", "definition.union: typedef union {", "definition.function: void print_array(int arr[], int size) {", "definition.function: int factorial(int n) {", "definition.function: void free_point(Point* p);", "definition.function: inline int max(int a, int b) {", "definition.function: void free_list(Node* head) {", "definition.function: int main(int argc, char* argv[]) {", "definition.enum: typedef enum {", "definition.function: void swap(int* a, int* b) {", "definition.function: void process_matrix(int matrix[][3], int rows) {", "definition.struct: typedef struct {", "definition.function: void free_point(Point* p) {", "definition.struct: typedef struct Node {"], "coverage": 85.82995951417004}, "cpp": {"success": true, "key_structures": 43, "chunks": 7, "structure_types": ["definition.constructor: void modernCppFeatures() {", "definition.constructor: Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {", "definition.class: class Point {", "definition.constructor: void print(Args... args) {", "definition.method: auto begin() { return data_.begin(); }", "definition.method: void setX(double x) { x_ = x; }", "definition.method: double perimeter() const override {", "definition.method: void add(bool value) {", "definition.method: auto end() { return data_.end(); }", "definition.constructor: int main() {", "definition.class: class Shape {", "definition.method: auto begin() const { return data_.begin(); }", "definition.operator: bool operator==(const Point& other) const {", "definition.method: double area() const override {", "definition.method: void draw() const override {", "definition.destructor: virtual ~Shape() = default;", "definition.constructor: Container() = default;", "definition.constructor: auto add(const T& a, const U& b) -> decltype(a + b) {", "definition.class: class Circle : public Shape {", "definition.constructor: Point() : x_(0.0), y_(0.0) {}", "definition.constructor: T max(const T& a, const T& b) {", "definition.class: class Container {", "definition.method: virtual void draw() const {", "definition.namespace: namespace geometry {", "definition.constructor: void add(U&& item) {", "definition.method: double getRadius() const { return radius_; }", "definition.method: static double distance(const Point& p1, const Point& p2) {", "definition.constructor: Container(std::initializer_list<T> init) : data_(init) {}", "definition.constructor: Circle(const Point& center, double radius, const std::string& color)", "definition.method: double getY() const { return y_; }", "definition.destructor: ~Point() = default;", "definition.constructor: Shape(const std::string& color) : color_(color) {}", "definition.template: template<typename T>", "definition.constructor: auto filter(Predicate pred) const {", "definition.operator: Point operator+(const Point& other) const {", "definition.operator: bool operator[](size_t index) const { return data_[index]; }", "definition.constructor: Point(double x, double y) : x_(x), y_(y) {}", "definition.method: size_t size() const { return data_.size(); }", "definition.constructor: Point(const Point& other) : x_(other.x_), y_(other.y_) {}", "definition.method: void setY(double y) { y_ = y; }", "definition.method: auto end() const { return data_.end(); }", "definition.method: double getX() const { return x_; }"], "coverage": 137.2340425531915}, "go": {"success": true, "key_structures": 14, "chunks": 9, "structure_types": ["name.definition.method: func (s *UserService) validateUser(user *User) error {", "name.definition.function: func SafeOperation() (err error) {", "name.definition.function: func main() {", "name.definition.function: func Sum[T Numeric](values []T) T {", "name.definition.function: func init() {", "name.definition.method: func (s *UserService) CreateUser(ctx context.Context, user *User) error {", "name.definition.method: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {", "name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {", "name.definition.function: func LogFields(msg string, fields ...interface{}) {", "name.definition.method: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {", "name.definition.function: func riskyOperation() {", "name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {", "name.definition.method: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {", "name.definition.method: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {"], "coverage": 69.00958466453673}, "rust": {"success": true, "key_structures": 38, "chunks": 11, "structure_types": ["definition.function: fn serialize(&self) -> String {", "definition.function: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {", "definition.enum: enum DatabaseError {", "definition.function: fn new(id: UserId, name: UserName, email: String) -> Self {", "definition.function: fn closure_examples() {", "definition.function: fn create_user(&self, name: User<PERSON><PERSON>, email: String) -> Result<UserId> {", "definition.function: fn main() -> Result<()> {", "definition.function: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {", "definition.struct: struct User {", "definition.function: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>", "definition.function: fn len(&self) -> usize {", "definition.struct: struct UserService {", "definition.function: fn process_user_status(status: &UserStatus) -> String {", "definition.method_container: impl<T: Clone> Clone for Repository<T> {", "definition.function: fn insert(&mut self, id: UserId, item: T) -> Result<()> {", "definition.function: fn get_user(&self, id: UserId) -> Option<User> {", "definition.function: fn test_user_activation() {", "definition.function: fn clone(&self) -> Self {", "definition.function: fn activate(&mut self) {", "definition.function: fn generate_user_id() -> UserId {", "definition.function: async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {", "definition.function: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {", "definition.function: fn validate(&self) -> Result<()> {", "definition.function: fn divide(a: f64, b: f64) -> Result<f64> {", "definition.function: fn is_active(&self) -> bool {", "definition.enum: enum UserStatus {", "definition.struct: struct Repository<T> {", "definition.function: fn into_name(self) -> UserName {", "definition.function: fn generate_verification_code() -> String {", "definition.function: fn new() -> Self {", "definition.function: fn activate_user(&self, id: UserId) -> Result<()> {", "definition.function: fn get(&self, id: &UserId) -> Option<&T> {", "definition.function: fn remove(&mut self, id: &UserId) -> Option<T> {", "definition.function: fn new(max_size: usize) -> Self {", "definition.function: fn test_user_creation() {", "definition.method_container: impl<T> Repository<T> {", "definition.function: fn test_repository_operations() {"], "coverage": 95.52631578947368}, "php": {"success": true, "key_structures": 58, "chunks": 12, "structure_types": ["definition.method: public function __construct()", "definition.method: public function save(User $user): bool;", "definition.method: public function jsonSerialize(): array", "definition.method: public function createUser(string $name, string $email): ?User", "definition.method: public function addRole(string $role): self", "definition.method: public function validate(): array", "definition.method: public function save(User $user): bool", "definition.method: public function getCreatedAt(): DateTime", "definition.method: public function setAvatar(?string $avatar): self", "definition.method: public function toArray(): array", "definition.method: public function touch(): void", "definition.method: public function __construct(PDO $pdo, LoggerInterface $logger)", "definition.method: public function getLabel(): string", "definition.method: public function suspend(): self", "definition.interface: interface LoggerInterface", "definition.method: public function findAll(): array;", "definition.method: public function error(string $message, array $context = []): void {", "definition.method: public function getEmail(): string", "definition.interface: interface UserRepositoryInterface", "definition.method: public function __toString(): string", "definition.method: public function info(string $message, array $context = []): void {", "definition.method: public function getStatus(): UserStatus", "definition.method: private function hydrate(array $data): User", "definition.method: public function info(string $message, array $context = []): void;", "definition.method: public function delete(int $id): bool;", "definition.method: abstract public function validate(): array;", "definition.method: public function activate(): self", "definition.method: public function setId(int $id): void", "definition.method: private function insert(User $user): bool", "definition.method: public function getName(): string", "definition.method: public function setName(string $name): self", "definition.method: public function setEmail(string $email): self", "definition.class: class UserRepository implements UserRepositoryInterface", "definition.method: public function removeRole(string $role): self", "definition.method: public function getAvatar(): ?string", "definition.method: public function isValid(): bool", "definition.class: class UserService", "definition.method: public function findAll(): array", "definition.method: public function activateUser(int $id): bool", "definition.method: public function getRoles(): array", "definition.method: public function delete(int $id): bool", "definition.method: public function error(string $message, array $context = []): void;", "definition.method: public function getId(): int", "definition.method: public function __clone()", "definition.method: public function setStatus(UserStatus $status): self", "definition.method: abstract public function toArray(): array;", "definition.method: public function hasRole(string $role): bool", "definition.class: abstract class BaseModel implements JsonSerializable", "definition.function: function generateRandomString(int $length = 10): string", "definition.method: public function findById(int $id): ?User", "definition.method: public function isActive(): bool", "definition.function: function validateEmail(string $email): bool", "definition.method: public function findById(int $id): ?User;", "definition.method: public function getUpdatedAt(): DateTime", "definition.class: class User extends BaseModel", "definition.method: private function update(User $user): bool", "definition.method: public function __construct("], "coverage": 144.91682070240296}, "ruby": {"success": false, "error": "list index out of range"}, "swift": {"success": true, "key_structures": 34, "chunks": 8, "structure_types": ["definition.interface: protocol UserRepositoryProtocol {", "definition.method: func deleteUser(by id: Int) -> Bool {", "definition.method: func warning(_ message: String) {", "definition.class: enum NetworkError: Error {", "definition.class: class Logger {", "definition.method: func validateEmail(_ email: String) -> Bool {", "definition.method: func activateUser(id: Int) -> Bool {", "definition.method: func error(_ message: String) {", "definition.method: mutating func updateName(_ newName: String) {", "definition.method: static func createSampleUser() -> User {", "definition.class: class UserRepository: UserRepositoryProtocol {", "definition.method: func saveUser(_ user: User) -> Bool {", "definition.method: func info(_ message: String) {", "definition.interface: protocol Validatable {", "definition.class: class UserService {", "definition.class: enum UserStatus: String, CaseIterable {", "definition.method: func delete(by key: String) -> Bool {", "definition.class: class Repository<T: Codable> {", "definition.method: func toJSON() -> String? {", "definition.method: func exampleUsage() {", "definition.method: private func log(_ message: String, level: Level) {", "definition.method: private func isValidEmail(_ email: String) -> Bool {", "definition.method: func findUser(by id: Int) -> User? {", "definition.method: func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {", "definition.method: func find(by key: String) -> T? {", "definition.method: func getAllUsers() -> [User] {", "definition.method: func all() -> [T] {", "definition.class: struct User: Codable, Validatable {", "definition.method: func save(_ item: T, with key: String) {", "definition.method: private func generateUserId() -> Int {", "definition.method: mutating func activate() {", "definition.class: enum Level: String {", "definition.method: func validate() -> [String] {", "definition.method: func createUser(name: String, email: String) -> Result<User, NetworkError> {"], "coverage": 92.28486646884274}, "kotlin": {"success": true, "key_structures": 65, "chunks": 9, "structure_types": ["definition.class: interface Logger {", "definition.function: suspend fun save(item: T): Result<T> = try {", "definition.function: private fun generateUserId(): Long = System.currentTimeMillis()", "definition.class: @Serializable", "definition.function: fun createSampleUser(): User = User(", "definition.function: fun updateName(newName: String) {", "definition.class: sealed class Result<out T> {", "definition.class: data class Updated(val user: User) : UserEvent()", "definition.function: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {", "definition.function: protected abstract suspend fun doSave(item: T): <PERSON><PERSON><PERSON>", "definition.function: fun debug(message: String) = log(LogLevel.DEBUG, message)", "definition.function: override fun validate(item: User): List<String> {", "definition.class: data class Deleted(val userId: Long) : UserEvent()", "definition.function: override fun log(level: LogLevel, message: String, throwable: Throwable?) {", "definition.function: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {", "definition.function: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {", "definition.class: class UserValidator : Validator<User> {", "definition.function: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)", "definition.class: abstract class BaseRepository<T, ID> {", "definition.class: sealed class UserEvent {", "definition.function: suspend fun delete(id: Long): <PERSON><PERSON><PERSON>", "definition.function: private fun isValidEmail(email: String): <PERSON><PERSON><PERSON> {", "definition.function: fun create(): ApiClient = ApiClient()", "definition.function: fun createWithTimeout(timeoutMs: Long): ApiClient {", "definition.function: fun fromString(value: String): UserStatus? {", "definition.class: enum class UserStatus(val displayName: String) {", "definition.function: suspend fun findById(id: Long): User?", "definition.function: fun createUsers(count: Int): List<User> = (1..count).map { i ->", "definition.function: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)", "definition.class: class UserService(", "definition.class: interface Validator<T> {", "definition.function: suspend fun findAll(): List<User>", "definition.class: interface UserRepository {", "definition.class: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()", "definition.function: fun List<User>.activeUsers(): List<User> = filter { it.isActive }", "definition.function: suspend fun save(user: User): <PERSON><PERSON><PERSON>", "definition.function: private fun notifyListeners(event: UserEvent) {", "definition.function: fun <T> List<T>.chunked(size: Int): List<List<T>> {", "definition.function: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }", "definition.function: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {", "definition.class: data class Success<T>(val data: T) : Result<T>()", "definition.class: class InMemoryUserRepository : UserRepository {", "definition.function: fun log(level: LogLevel, message: String, throwable: Throwable? = null)", "definition.function: fun addEventListener(listener: (UserEvent) -> Unit) {", "definition.function: fun activate() {", "definition.function: protected abstract suspend fun doFindById(id: ID): T?", "definition.class: class ApiClient {", "definition.class: data class Created(val user: User) : UserEvent()", "definition.function: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {", "definition.function: suspend fun getUserStats(): Map<UserStatus, Int> = try {", "definition.function: fun info(message: String) = log(LogLevel.INFO, message)", "definition.function: fun isValid(item: T): Boolean = validate(item).isEmpty()", "definition.function: protected abstract suspend fun doDelete(id: ID): <PERSON><PERSON><PERSON>", "definition.function: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {", "definition.function: fun String.toUser(): User? = try {", "definition.function: suspend fun activateUser(id: Long): Boolean = try {", "definition.function: suspend fun <T> retryOperation(", "definition.function: suspend fun createUser(name: String, email: String): Result<User> = try {", "definition.function: suspend fun findByStatus(status: UserStatus): List<User>", "definition.function: fun validate(item: T): List<String>", "definition.class: enum class LogLevel {", "definition.class: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()", "definition.class: class ConsoleLogger : Logger {", "definition.function: suspend fun main() {"], "coverage": 94.02597402597402}, "scala": {"success": true, "key_structures": 38, "chunks": 10, "structure_types": ["definition.method: override def findAll(): Future[List[User]] = Future {", "definition.method: def createUser(name: String, email: String): Future[Result[User]] = {", "definition.method: def activateUser(id: Long): Future[Boolean] = {", "definition.method: def createSampleUser(): User = User(", "definition.class: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {", "definition.class: implicit class UserOps(user: User) {", "definition.method: def extractUserInfo(user: User): (String, String) = user match {", "definition.method: def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T])", "definition.method: def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {", "definition.class: class UserService(", "definition.method: override def validate(item: User): List[ValidationError] = {", "definition.method: def toJson: String = {", "definition.method: override def error(message: String, throwable: Option[Throwable] = None): Unit = {", "definition.method: override def findById(id: Long): Future[Option[User]] = Future {", "definition.method: def isOlder<PERSON>han(days: Int): Bo<PERSON>an = {", "definition.method: private def isValidEmail(email: String): Boolean = {", "definition.method: def addEventListener(listener: UserEvent => Unit): Unit = {", "definition.class: class ApiClient private(baseUrl: String, timeoutMs: Long) {", "definition.class: case class User(", "definition.method: def fromString(value: String): Option[UserStatus] = value.toLowerCase match {", "definition.class: abstract class BaseRepository[T, ID] {", "definition.method: override def delete(id: Long): Future[Boolean] = Future {", "definition.method: override def debug(message: String): Unit = {", "definition.method: def attempt(remaining: Int): Future[T] = {", "definition.method: def createUsers(count: Int): List[User] = (1 to count).map { i =>", "definition.namespace: package com.example.sample", "definition.class: class UserValidator extends Validator[User] {", "definition.method: override def save(user: User): Future[Boolean] = Future {", "definition.method: private def notifyListeners(event: UserEvent): Unit = {", "definition.class: class ConsoleLogger extends Logger {", "definition.method: def processResult[T](result: Result[T]): String = result match {", "definition.method: def getUserStats(): Future[Map[UserStatus, Int]] = {", "definition.method: def processUser(user: User): String = user match {", "definition.method: override def info(message: String): Unit = {", "definition.class: implicit class UserListOps(users: List[User]) {", "definition.method: override def findByStatus(status: UserStatus): Future[List[User]] = Future {", "definition.method: def measureTime[T](operation: () => T): (T, Long) = {", "definition.method: def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {"], "coverage": 96.54377880184332}, "html": {"success": false, "error": "list index out of range"}, "css": {"success": true, "key_structures": 0, "chunks": 16, "structure_types": [], "coverage": 93.65079365079364}, "jsx": {"success": true, "key_structures": 0, "chunks": 0, "structure_types": [], "coverage": 0.0}, "tsx": {"success": true, "key_structures": 7, "chunks": 12, "structure_types": ["definition.interface: interface FormErrors {", "definition.interface: interface UserStats {", "definition.interface: interface User {", "definition.interface: interface UserStatsProps {", "definition.interface: interface LoadingProps {", "definition.interface: interface UserProfileProps {", "definition.interface: interface UserPreferences {"], "coverage": 137.109375}}}