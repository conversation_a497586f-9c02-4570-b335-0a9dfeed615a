FileParser 解析结果报告 - PHP
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php
  文件名: sample.php
  内容长度: 13449 字符
  行数: 541

关键结构行 (8 个):
  第  43 行: name.definition.interface: interface UserRepositoryInterface
           内容: interface UserRepositoryInterface
  第  51 行: name.definition.interface: interface LoggerInterface
           内容: interface LoggerInterface
  第  90 行: name.definition.class: abstract class BaseModel implements JsonSerializable
           内容: abstract class BaseModel implements JsonSerializable
  第 121 行: name.definition.class: class User extends BaseModel
           内容: class User extends BaseModel
  第 285 行: name.definition.class: class UserRepository implements UserRepositoryInterface
           内容: class UserRepository implements UserRepositoryInterface
  第 433 行: name.definition.class: class UserService
           内容: class UserService
  第 480 行: name.definition.function: function generateRandomString(int $length = 10): string
           内容: function generateRandomString(int $length = 10): string
  第 493 行: name.definition.function: function validateEmail(string $email): bool
           内容: function validateEmail(string $email): bool

检测到的结构类型:
  - name.definition.class: abstract class BaseModel implements JsonSerializable: 1 个
  - name.definition.class: class User extends BaseModel: 1 个
  - name.definition.class: class UserRepository implements UserRepositoryInterface: 1 个
  - name.definition.class: class UserService: 1 个
  - name.definition.function: function generateRandomString(int $length = 10): string: 1 个
  - name.definition.function: function validateEmail(string $email): bool: 1 个
  - name.definition.interface: interface LoggerInterface: 1 个
  - name.definition.interface: interface UserRepositoryInterface: 1 个

代码块信息 (42 个):
  块 1: 第 31-49 行 (19 行)
     31:     public function getLabel(): string
     32:     {
     33:         return match($this) {
    ... (还有 16 行)

  块 2: 第 43-55 行 (13 行)
     43: interface UserRepositoryInterface
     44: {
     45:     public function findById(int $id): ?User;
    ... (还有 10 行)

  块 3: 第 51-66 行 (16 行)
     51: interface LoggerInterface
     52: {
     53:     public function info(string $message, array $context = []): void;
    ... (还有 13 行)

  块 4: 第 63-76 行 (14 行)
     63:     public function getCreatedAt(): DateTime
     64:     {
     65:         return $this->createdAt;
    ... (还有 11 行)

  块 5: 第 73-86 行 (14 行)
     73:     public function touch(): void
     74:     {
     75:         $this->updatedAt = new DateTime();
    ... (还有 11 行)

  块 6: 第 83-118 行 (36 行)
     83:     public function isValid(): bool
     84:     {
     85:         return empty($this->validate());
    ... (还有 33 行)

  块 7: 第 90-100 行 (11 行)
     90: abstract class BaseModel implements JsonSerializable
     91: {
     92:     use Timestampable;
    ... (还有 8 行)

  块 8: 第 96-110 行 (15 行)
     96:     public function __construct()
     97:     {
     98:         $this->createdAt = new DateTime();
    ... (还有 12 行)

  块 9: 第 107-117 行 (11 行)
    107:     public function setId(int $id): void
    108:     {
    109:         $this->id = $id;
    ... (还有 8 行)

  块 10: 第 114-114 行 (1 行)
    114:     public function jsonSerialize(): array

  块 11: 第 121-142 行 (22 行)
    121: class User extends BaseModel
    122: {
    123:     use Validatable;
    ... (还有 19 行)

  块 12: 第 132-148 行 (17 行)
    132:     public function __construct(
    133:         string $name,
    134:         string $email,
    ... (还有 14 行)

  块 13: 第 145-158 行 (14 行)
    145:     public function getName(): string
    146:     {
    147:         return $this->name;
    ... (还有 11 行)

  块 14: 第 155-168 行 (14 行)
    155:     public function getStatus(): UserStatus
    156:     {
    157:         return $this->status;
    ... (还有 11 行)

  块 15: 第 165-176 行 (12 行)
    165:     public function getAvatar(): ?string
    166:     {
    167:         return $this->avatar;
    ... (还有 9 行)

  块 16: 第 171-183 行 (13 行)
    171:     public function setName(string $name): self
    172:     {
    173:         $this->name = $name;
    ... (还有 10 行)

  块 17: 第 178-190 行 (13 行)
    178:     public function setEmail(string $email): self
    179:     {
    180:         $this->email = $email;
    ... (还有 10 行)

  块 18: 第 185-197 行 (13 行)
    185:     public function setStatus(UserStatus $status): self
    186:     {
    187:         $this->status = $status;
    ... (还有 10 行)

  块 19: 第 192-207 行 (16 行)
    192:     public function setAvatar(?string $avatar): self
    193:     {
    194:         $this->avatar = $avatar;
    ... (还有 13 行)

  块 20: 第 200-214 行 (15 行)
    200:     public function addRole(string $role): self
    201:     {
    202:         if (!in_array($role, $this->roles)) {
    ... (还有 12 行)

  块 21: 第 209-219 行 (11 行)
    209:     public function removeRole(string $role): self
    210:     {
    211:         $this->roles = array_filter($this->roles, fn($r) => $r !== $role);
    ... (还有 8 行)

  块 22: 第 216-230 行 (15 行)
    216:     public function hasRole(string $role): bool
    217:     {
    218:         return in_array($role, $this->roles);
    ... (还有 12 行)

  块 23: 第 227-253 行 (27 行)
    227:     public function activate(): self
    228:     {
    229:         return $this->setStatus(UserStatus::ACTIVE);
    ... (还有 24 行)

  块 24: 第 238-268 行 (31 行)
    238:     public function validate(): array
    239:     {
    240:         $errors = [];
    ... (还有 28 行)

  块 25: 第 256-274 行 (19 行)
    256:     public function toArray(): array
    257:     {
    258:         return [
    ... (还有 16 行)

  块 26: 第 271-281 行 (11 行)
    271:     public function __toString(): string
    272:     {
    273:         return sprintf('%s <%s>', $this->name, $this->email);
    ... (还有 8 行)

  块 27: 第 276-276 行 (1 行)
    276:     public function __clone()

  块 28: 第 285-315 行 (31 行)
    285: class UserRepository implements UserRepositoryInterface
    286: {
    287:     private PDO $pdo;
    ... (还有 28 行)

  块 29: 第 296-332 行 (37 行)
    296:     public function findById(int $id): ?User
    297:     {
    298:         try {
    ... (还有 34 行)

  块 30: 第 317-349 行 (33 行)
    317:     public function save(User $user): bool
    318:     {
    319:         try {
    ... (还有 30 行)

  块 31: 第 334-368 行 (35 行)
    334:     public function delete(int $id): bool
    335:     {
    336:         try {
    ... (还有 32 行)

  块 32: 第 351-391 行 (41 行)
    351:     public function findAll(): array
    352:     {
    353:         try {
    ... (还有 38 行)

  块 33: 第 370-409 行 (40 行)
    370:     private function insert(User $user): bool
    371:     {
    372:         $sql = 'INSERT INTO users (name, email, status, roles, avatar, created_at, updated_at) 
    ... (还有 37 行)

  块 34: 第 393-429 行 (37 行)
    393:     private function update(User $user): bool
    394:     {
    395:         $sql = 'UPDATE users SET name = :name, email = :email, status = :status, 
    ... (还有 34 行)

  块 35: 第 411-411 行 (1 行)
    411:     private function hydrate(array $data): User

  块 36: 第 433-444 行 (12 行)
    433: class UserService
    434: {
    435:     private UserRepositoryInterface $repository;
    ... (还有 9 行)

  块 37: 第 438-463 行 (26 行)
    438:     public function __construct(
    439:         UserRepositoryInterface $repository,
    440:         LoggerInterface $logger
    ... (还有 23 行)

  块 38: 第 446-476 行 (31 行)
    446:     public function createUser(string $name, string $email): ?User
    447:     {
    448:         $user = new User($name, $email);
    ... (还有 28 行)

  块 39: 第 465-491 行 (27 行)
    465:     public function activateUser(int $id): bool
    466:     {
    467:         $user = $this->repository->findById($id);
    ... (还有 24 行)

  块 40: 第 480-496 行 (17 行)
    480: function generateRandomString(int $length = 10): string
    481: {
    482:     $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    ... (还有 14 行)

  块 41: 第 493-519 行 (27 行)
    493: function validateEmail(string $email): bool
    494: {
    495:     return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    ... (还有 24 行)

  块 42: 第 517-517 行 (1 行)
    517:         public function info(string $message, array $context = []): void {


统计信息:
  覆盖率: 149.7%
  块中总行数: 810
  结构类型数: 8
