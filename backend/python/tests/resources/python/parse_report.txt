FileParser 解析结果报告 - PYTHON
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py
  文件名: sample.py
  内容长度: 3946 字符
  行数: 169

关键结构行 (27 个):
  第  17 行: definition.class: @dataclass
           内容: @dataclass
  第  18 行: definition.class: class Person:
           内容: class Person:
  第  24 行: definition.function: def __post_init__(self):
           内容: def __post_init__(self):
  第  29 行: definition.class: class Animal(ABC):
           内容: class Animal(ABC):
  第  32 行: definition.function: def __init__(self, name: str, species: str):
           内容: def __init__(self, name: str, species: str):
  第  36 行: definition.function: @abstractmethod
           内容: @abstractmethod
  第  37 行: definition.function: def make_sound(self) -> str:
           内容: def make_sound(self) -> str:
  第  41 行: definition.function: @property
           内容: @property
  第  42 行: definition.function: def description(self) -> str:
           内容: def description(self) -> str:
  第  46 行: definition.class: class Dog(Animal):
           内容: class Dog(Animal):
  第  49 行: definition.function: def __init__(self, name: str, breed: str):
           内容: def __init__(self, name: str, breed: str):
  第  53 行: definition.function: def make_sound(self) -> str:
           内容: def make_sound(self) -> str:
  第  56 行: definition.function: @staticmethod
           内容: @staticmethod
  第  57 行: definition.function: def is_good_boy() -> bool:
           内容: def is_good_boy() -> bool:
  第  60 行: definition.function: @classmethod
           内容: @classmethod
  第  61 行: definition.function: def create_puppy(cls, name: str, breed: str) -> 'Dog':
           内容: def create_puppy(cls, name: str, breed: str) -> 'Dog':
  第  65 行: definition.function: def simple_function(x: int, y: int = 10) -> int:
           内容: def simple_function(x: int, y: int = 10) -> int:
  第  70 行: definition.function: async def async_function(data: List[str]) -> Dict[str, int]:
           内容: async def async_function(data: List[str]) -> Dict[str, int]:
  第  78 行: definition.function: def generator_function(n: int):
           内容: def generator_function(n: int):
  第  84 行: definition.function: @property
           内容: @property
  第  85 行: definition.function: def decorated_function():
           内容: def decorated_function():
  第 104 行: definition.function: def function_with_context_managers():
           内容: def function_with_context_managers():
  第 113 行: definition.function: def function_with_exception_handling():
           内容: def function_with_exception_handling():
  第 127 行: definition.function: def function_with_global_nonlocal():
           内容: def function_with_global_nonlocal():
  第 131 行: definition.function: def inner_function():
           内容: def inner_function():
  第 141 行: definition.function: def match_case_example(value):
           内容: def match_case_example(value):
  第 155 行: definition.function: def typed_function(
           内容: def typed_function(

检测到的结构类型:
  - definition.class: @dataclass: 1 个
  - definition.class: class Animal(ABC):: 1 个
  - definition.class: class Dog(Animal):: 1 个
  - definition.class: class Person:: 1 个
  - definition.function: @abstractmethod: 1 个
  - definition.function: @classmethod: 1 个
  - definition.function: @property: 2 个
  - definition.function: @staticmethod: 1 个
  - definition.function: async def async_function(data: List[str]) -> Dict[str, int]:: 1 个
  - definition.function: def __init__(self, name: str, breed: str):: 1 个
  - definition.function: def __init__(self, name: str, species: str):: 1 个
  - definition.function: def __post_init__(self):: 1 个
  - definition.function: def create_puppy(cls, name: str, breed: str) -> 'Dog':: 1 个
  - definition.function: def decorated_function():: 1 个
  - definition.function: def description(self) -> str:: 1 个
  - definition.function: def function_with_context_managers():: 1 个
  - definition.function: def function_with_exception_handling():: 1 个
  - definition.function: def function_with_global_nonlocal():: 1 个
  - definition.function: def generator_function(n: int):: 1 个
  - definition.function: def inner_function():: 1 个
  - definition.function: def is_good_boy() -> bool:: 1 个
  - definition.function: def make_sound(self) -> str:: 2 个
  - definition.function: def match_case_example(value):: 1 个
  - definition.function: def simple_function(x: int, y: int = 10) -> int:: 1 个
  - definition.function: def typed_function(: 1 个

代码块信息 (6 个):
  块 1: 第 6-26 行 (21 行)
      6: import os
      7: import sys
      8: from typing import List, Dict, Optional
    ... (还有 18 行)

  块 2: 第 29-62 行 (34 行)
     29: class Animal(ABC):
     30:     """Abstract base class for animals."""
     31:     
    ... (还有 31 行)

  块 3: 第 65-87 行 (23 行)
     65: def simple_function(x: int, y: int = 10) -> int:
     66:     """A simple function with type annotations."""
     67:     return x + y
    ... (还有 20 行)

  块 4: 第 95-124 行 (30 行)
     95: numbers = [x for x in range(10) if x % 2 == 0]
     96: 
     97: # Dictionary comprehension
    ... (还有 27 行)

  块 5: 第 127-151 行 (25 行)
    127: def function_with_global_nonlocal():
    128:     """Function demonstrating global and nonlocal statements."""
    129:     global global_var
    ... (还有 22 行)

  块 6: 第 155-161 行 (7 行)
    155: def typed_function(
    156:     param1: str,
    157:     param2: List[int],
    ... (还有 4 行)


统计信息:
  覆盖率: 82.8%
  块中总行数: 140
  结构类型数: 25
