FileParser 解析结果报告 - RUST
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs
  文件名: sample.rs
  内容长度: 9573 字符
  行数: 380

关键结构行 (36 个):
  第  20 行: name.definition.enum: enum UserStatus {
           内容: enum UserStatus {
  第  28 行: name.definition.enum: enum DatabaseError {
           内容: enum DatabaseError {
  第  35 行: name.definition.function: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
           内容: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
  第  48 行: name.definition.struct: struct User {
           内容: struct User {
  第  58 行: name.definition.function: fn new(id: UserId, name: UserName, email: String) -> Self {
           内容: fn new(id: UserId, name: UserName, email: String) -> Self {
  第  71 行: name.definition.function: fn activate(&mut self) {
           内容: fn activate(&mut self) {
  第  76 行: name.definition.function: fn is_active(&self) -> bool {
           内容: fn is_active(&self) -> bool {
  第  81 行: name.definition.function: fn into_name(self) -> UserName {
           内容: fn into_name(self) -> UserName {
  第  87 行: name.definition.function: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
           内容: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
  第  94 行: name.definition.struct: struct Repository<T> {
           内容: struct Repository<T> {
  第 100 行: name.definition.function: fn new(max_size: usize) -> Self {
           内容: fn new(max_size: usize) -> Self {
  第 107 行: name.definition.function: fn insert(&mut self, id: UserId, item: T) -> Result<()> {
           内容: fn insert(&mut self, id: UserId, item: T) -> Result<()> {
  第 115 行: name.definition.function: fn get(&self, id: &UserId) -> Option<&T> {
           内容: fn get(&self, id: &UserId) -> Option<&T> {
  第 119 行: name.definition.function: fn remove(&mut self, id: &UserId) -> Option<T> {
           内容: fn remove(&mut self, id: &UserId) -> Option<T> {
  第 123 行: name.definition.function: fn len(&self) -> usize {
           内容: fn len(&self) -> usize {
  第 138 行: name.definition.function: fn validate(&self) -> Result<()> {
           内容: fn validate(&self) -> Result<()> {
  第 150 行: name.definition.function: fn serialize(&self) -> String {
           内容: fn serialize(&self) -> String {
  第 157 行: name.definition.function: fn clone(&self) -> Self {
           内容: fn clone(&self) -> Self {
  第 167 行: name.definition.struct: struct UserService {
           内容: struct UserService {
  第 172 行: name.definition.function: fn new() -> Self {
           内容: fn new() -> Self {
  第 178 行: name.definition.function: fn create_user(&self, name: UserName, email: String) -> Result<UserId> {
           内容: fn create_user(&self, name: UserName, email: String) -> Result<UserId> {
  第 190 行: name.definition.function: fn get_user(&self, id: UserId) -> Option<User> {
           内容: fn get_user(&self, id: UserId) -> Option<User> {
  第 195 行: name.definition.function: fn activate_user(&self, id: UserId) -> Result<()> {
           内容: fn activate_user(&self, id: UserId) -> Result<()> {
  第 206 行: name.definition.function: async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {
           内容: async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {
  第 223 行: name.definition.function: fn generate_user_id() -> UserId {
           内容: fn generate_user_id() -> UserId {
  第 229 行: name.definition.function: fn generate_verification_code() -> String {
           内容: fn generate_verification_code() -> String {
  第 236 行: name.definition.function: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>
           内容: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>
  第 244 行: name.definition.function: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {
           内容: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {
  第 253 行: name.definition.function: fn closure_examples() {
           内容: fn closure_examples() {
  第 269 行: name.definition.function: fn process_user_status(status: &UserStatus) -> String {
           内容: fn process_user_status(status: &UserStatus) -> String {
  第 281 行: name.definition.function: fn divide(a: f64, b: f64) -> Result<f64> {
           内容: fn divide(a: f64, b: f64) -> Result<f64> {
  第 290 行: name.definition.function: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {
           内容: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {
  第 307 行: name.definition.function: fn test_user_creation() {
           内容: fn test_user_creation() {
  第 315 行: name.definition.function: fn test_user_activation() {
           内容: fn test_user_activation() {
  第 322 行: name.definition.function: fn test_repository_operations() {
           内容: fn test_repository_operations() {
  第 333 行: name.definition.function: fn main() -> Result<()> {
           内容: fn main() -> Result<()> {

检测到的结构类型:
  - name.definition.enum: enum DatabaseError {: 1 个
  - name.definition.enum: enum UserStatus {: 1 个
  - name.definition.function: async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {: 1 个
  - name.definition.function: fn activate(&mut self) {: 1 个
  - name.definition.function: fn activate_user(&self, id: UserId) -> Result<()> {: 1 个
  - name.definition.function: fn clone(&self) -> Self {: 1 个
  - name.definition.function: fn closure_examples() {: 1 个
  - name.definition.function: fn create_user(&self, name: UserName, email: String) -> Result<UserId> {: 1 个
  - name.definition.function: fn divide(a: f64, b: f64) -> Result<f64> {: 1 个
  - name.definition.function: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>: 1 个
  - name.definition.function: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {: 1 个
  - name.definition.function: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {: 2 个
  - name.definition.function: fn generate_user_id() -> UserId {: 1 个
  - name.definition.function: fn generate_verification_code() -> String {: 1 个
  - name.definition.function: fn get(&self, id: &UserId) -> Option<&T> {: 1 个
  - name.definition.function: fn get_user(&self, id: UserId) -> Option<User> {: 1 个
  - name.definition.function: fn insert(&mut self, id: UserId, item: T) -> Result<()> {: 1 个
  - name.definition.function: fn into_name(self) -> UserName {: 1 个
  - name.definition.function: fn is_active(&self) -> bool {: 1 个
  - name.definition.function: fn len(&self) -> usize {: 1 个
  - name.definition.function: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {: 1 个
  - name.definition.function: fn main() -> Result<()> {: 1 个
  - name.definition.function: fn new() -> Self {: 1 个
  - name.definition.function: fn new(id: UserId, name: UserName, email: String) -> Self {: 1 个
  - name.definition.function: fn new(max_size: usize) -> Self {: 1 个
  - name.definition.function: fn process_user_status(status: &UserStatus) -> String {: 1 个
  - name.definition.function: fn remove(&mut self, id: &UserId) -> Option<T> {: 1 个
  - name.definition.function: fn serialize(&self) -> String {: 1 个
  - name.definition.function: fn test_repository_operations() {: 1 个
  - name.definition.function: fn test_user_activation() {: 1 个
  - name.definition.function: fn test_user_creation() {: 1 个
  - name.definition.function: fn validate(&self) -> Result<()> {: 1 个
  - name.definition.struct: struct Repository<T> {: 1 个
  - name.definition.struct: struct User {: 1 个
  - name.definition.struct: struct UserService {: 1 个

代码块信息 (39 个):
  块 1: 第 3-14 行 (12 行)
      3: use std::collections::HashMap;
      4: use std::fmt::{Display, Formatter, Result as FmtResult};
      5: use std::sync::{Arc, Mutex};
    ... (还有 9 行)

  块 2: 第 14-25 行 (12 行)
     14: type UserId = u64;
     15: type UserName = String;
     16: type Result<T> = std::result::Result<T, Box<dyn std::error::Error>>;
    ... (还有 9 行)

  块 3: 第 20-32 行 (13 行)
     20: enum UserStatus {
     21:     Active,
     22:     Inactive,
    ... (还有 10 行)

  块 4: 第 28-42 行 (15 行)
     28: enum DatabaseError {
     29:     ConnectionFailed,
     30:     QueryFailed(String),
    ... (还有 12 行)

  块 5: 第 34-44 行 (11 行)
     34: impl Display for DatabaseError {
     35:     fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
     36:         match self {
    ... (还有 8 行)

  块 6: 第 44-54 行 (11 行)
     44: impl std::error::Error for DatabaseError {}
     45: 
     46: // Structs
    ... (还有 8 行)

  块 7: 第 48-84 行 (37 行)
     48: struct User {
     49:     id: UserId,
     50:     name: UserName,
    ... (还有 34 行)

  块 8: 第 56-68 行 (13 行)
     56: impl User {
     57:     // Associated function (constructor)
     58:     fn new(id: UserId, name: UserName, email: String) -> Self {
    ... (还有 10 行)

  块 9: 第 58-73 行 (16 行)
     58:     fn new(id: UserId, name: UserName, email: String) -> Self {
     59:         Self {
     60:             id,
    ... (还有 13 行)

  块 10: 第 71-83 行 (13 行)
     71:     fn activate(&mut self) {
     72:         self.status = UserStatus::Active;
     73:     }
    ... (还有 10 行)

  块 11: 第 81-93 行 (13 行)
     81:     fn into_name(self) -> UserName {
     82:         self.name
     83:     }
    ... (还有 10 行)

  块 12: 第 93-126 行 (34 行)
     93: #[derive(Debug)]
     94: struct Repository<T> {
     95:     data: HashMap<UserId, T>,
    ... (还有 31 行)

  块 13: 第 100-113 行 (14 行)
    100:     fn new(max_size: usize) -> Self {
    101:         Self {
    102:             data: HashMap::new(),
    ... (还有 11 行)

  块 14: 第 107-117 行 (11 行)
    107:     fn insert(&mut self, id: UserId, item: T) -> Result<()> {
    108:         if self.data.len() >= self.max_size {
    109:             return Err("Repository is full".into());
    ... (还有 8 行)

  块 15: 第 115-125 行 (11 行)
    115:     fn get(&self, id: &UserId) -> Option<&T> {
    116:         self.data.get(id)
    117:     }
    ... (还有 8 行)

  块 16: 第 123-135 行 (13 行)
    123:     fn len(&self) -> usize {
    124:         self.data.len()
    125:     }
    ... (还有 10 行)

  块 17: 第 133-147 行 (15 行)
    133: trait Serialize {
    134:     fn serialize(&self) -> String;
    135: }
    ... (还有 12 行)

  块 18: 第 137-153 行 (17 行)
    137: impl Validate for User {
    138:     fn validate(&self) -> Result<()> {
    139:         if self.name.is_empty() {
    ... (还有 14 行)

  块 19: 第 149-163 行 (15 行)
    149: impl Serialize for User {
    150:     fn serialize(&self) -> String {
    151:         format!("{}|{}|{}", self.id, self.name, self.email)
    ... (还有 12 行)

  块 20: 第 157-169 行 (13 行)
    157:     fn clone(&self) -> Self {
    158:         Self {
    159:             data: self.data.clone(),
    ... (还有 10 行)

  块 21: 第 167-167 行 (1 行)
    167: struct UserService {

  块 22: 第 171-188 行 (18 行)
    171: impl UserService {
    172:     fn new() -> Self {
    173:         Self {
    ... (还有 15 行)

  块 23: 第 178-193 行 (16 行)
    178:     fn create_user(&self, name: UserName, email: String) -> Result<UserId> {
    179:         let user_id = generate_user_id();
    180:         let user = User::new(user_id, name, email);
    ... (还有 13 行)

  块 24: 第 190-203 行 (14 行)
    190:     fn get_user(&self, id: UserId) -> Option<User> {
    191:         let repo = self.repository.lock().unwrap();
    192:         repo.get(&id).cloned()
    ... (还有 11 行)

  块 25: 第 195-219 行 (25 行)
    195:     fn activate_user(&self, id: UserId) -> Result<()> {
    196:         let mut repo = self.repository.lock().unwrap();
    197:         if let Some(user) = repo.data.get_mut(&id) {
    ... (还有 22 行)

  块 26: 第 206-227 行 (22 行)
    206:     async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {
    207:         let mut users = Vec::new();
    208:         
    ... (还有 19 行)

  块 27: 第 223-233 行 (11 行)
    223: fn generate_user_id() -> UserId {
    224:     use std::sync::atomic::{AtomicU64, Ordering};
    225:     static COUNTER: AtomicU64 = AtomicU64::new(1);
    ... (还有 8 行)

  块 28: 第 229-241 行 (13 行)
    229: fn generate_verification_code() -> String {
    230:     use rand::Rng;
    231:     let mut rng = rand::thread_rng();
    ... (还有 10 行)

  块 29: 第 236-250 行 (15 行)
    236: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>
    237: where
    238:     F: Fn(&T) -> bool,
    ... (还有 12 行)

  块 30: 第 244-266 行 (23 行)
    244: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {
    245:     if x.len() > y.len() {
    246:         x
    ... (还有 20 行)

  块 31: 第 253-278 行 (26 行)
    253: fn closure_examples() {
    254:     let numbers = vec![1, 2, 3, 4, 5];
    255:     
    ... (还有 23 行)

  块 32: 第 269-287 行 (19 行)
    269: fn process_user_status(status: &UserStatus) -> String {
    270:     match status {
    271:         UserStatus::Active => "User is active".to_string(),
    ... (还有 16 行)

  块 33: 第 281-292 行 (12 行)
    281: fn divide(a: f64, b: f64) -> Result<f64> {
    282:     if b == 0.0 {
    283:         Err("Division by zero".into())
    ... (还有 9 行)

  块 34: 第 290-302 行 (13 行)
    290: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {
    291:     users.iter().find(|user| user.email == email)
    292: }
    ... (还有 10 行)

  块 35: 第 302-330 行 (29 行)
    302: #[cfg(test)]
    303: mod tests {
    304:     use super::*;
    ... (还有 26 行)

  块 36: 第 303-314 行 (12 行)
    303: mod tests {
    304:     use super::*;
    305:     
    ... (还有 9 行)

  块 37: 第 314-329 行 (16 行)
    314:     #[test]
    315:     fn test_user_activation() {
    316:         let mut user = User::new(1, "John Doe".to_string(), "<EMAIL>".to_string());
    ... (还有 13 行)

  块 38: 第 322-322 行 (1 行)
    322:     fn test_repository_operations() {

  块 39: 第 333-358 行 (26 行)
    333: fn main() -> Result<()> {
    334:     log_info!("Starting user service");
    335:     
    ... (还有 23 行)


统计信息:
  覆盖率: 163.4%
  块中总行数: 621
  结构类型数: 35
