FileParser 解析结果报告 - RUST
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs
  文件名: sample.rs
  内容长度: 9573 字符
  行数: 380

关键结构行 (38 个):
  第  20 行: definition.enum: enum UserStatus {
           内容: enum UserStatus {
  第  28 行: definition.enum: enum DatabaseError {
           内容: enum DatabaseError {
  第  35 行: definition.function: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
           内容: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
  第  48 行: definition.struct: struct User {
           内容: struct User {
  第  58 行: definition.function: fn new(id: UserId, name: User<PERSON><PERSON>, email: String) -> Self {
           内容: fn new(id: UserId, name: UserN<PERSON>, email: String) -> Self {
  第  71 行: definition.function: fn activate(&mut self) {
           内容: fn activate(&mut self) {
  第  76 行: definition.function: fn is_active(&self) -> bool {
           内容: fn is_active(&self) -> bool {
  第  81 行: definition.function: fn into_name(self) -> UserName {
           内容: fn into_name(self) -> UserName {
  第  87 行: definition.function: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
           内容: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
  第  94 行: definition.struct: struct Repository<T> {
           内容: struct Repository<T> {
  第  99 行: definition.method_container: impl<T> Repository<T> {
           内容: impl<T> Repository<T> {
  第 100 行: definition.function: fn new(max_size: usize) -> Self {
           内容: fn new(max_size: usize) -> Self {
  第 107 行: definition.function: fn insert(&mut self, id: UserId, item: T) -> Result<()> {
           内容: fn insert(&mut self, id: UserId, item: T) -> Result<()> {
  第 115 行: definition.function: fn get(&self, id: &UserId) -> Option<&T> {
           内容: fn get(&self, id: &UserId) -> Option<&T> {
  第 119 行: definition.function: fn remove(&mut self, id: &UserId) -> Option<T> {
           内容: fn remove(&mut self, id: &UserId) -> Option<T> {
  第 123 行: definition.function: fn len(&self) -> usize {
           内容: fn len(&self) -> usize {
  第 138 行: definition.function: fn validate(&self) -> Result<()> {
           内容: fn validate(&self) -> Result<()> {
  第 150 行: definition.function: fn serialize(&self) -> String {
           内容: fn serialize(&self) -> String {
  第 156 行: definition.method_container: impl<T: Clone> Clone for Repository<T> {
           内容: impl<T: Clone> Clone for Repository<T> {
  第 157 行: definition.function: fn clone(&self) -> Self {
           内容: fn clone(&self) -> Self {
  第 167 行: definition.struct: struct UserService {
           内容: struct UserService {
  第 172 行: definition.function: fn new() -> Self {
           内容: fn new() -> Self {
  第 178 行: definition.function: fn create_user(&self, name: UserName, email: String) -> Result<UserId> {
           内容: fn create_user(&self, name: UserName, email: String) -> Result<UserId> {
  第 190 行: definition.function: fn get_user(&self, id: UserId) -> Option<User> {
           内容: fn get_user(&self, id: UserId) -> Option<User> {
  第 195 行: definition.function: fn activate_user(&self, id: UserId) -> Result<()> {
           内容: fn activate_user(&self, id: UserId) -> Result<()> {
  第 206 行: definition.function: async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {
           内容: async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {
  第 223 行: definition.function: fn generate_user_id() -> UserId {
           内容: fn generate_user_id() -> UserId {
  第 229 行: definition.function: fn generate_verification_code() -> String {
           内容: fn generate_verification_code() -> String {
  第 236 行: definition.function: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>
           内容: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>
  第 244 行: definition.function: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {
           内容: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {
  第 253 行: definition.function: fn closure_examples() {
           内容: fn closure_examples() {
  第 269 行: definition.function: fn process_user_status(status: &UserStatus) -> String {
           内容: fn process_user_status(status: &UserStatus) -> String {
  第 281 行: definition.function: fn divide(a: f64, b: f64) -> Result<f64> {
           内容: fn divide(a: f64, b: f64) -> Result<f64> {
  第 290 行: definition.function: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {
           内容: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {
  第 307 行: definition.function: fn test_user_creation() {
           内容: fn test_user_creation() {
  第 315 行: definition.function: fn test_user_activation() {
           内容: fn test_user_activation() {
  第 322 行: definition.function: fn test_repository_operations() {
           内容: fn test_repository_operations() {
  第 333 行: definition.function: fn main() -> Result<()> {
           内容: fn main() -> Result<()> {

检测到的结构类型:
  - definition.enum: enum DatabaseError {: 1 个
  - definition.enum: enum UserStatus {: 1 个
  - definition.function: async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {: 1 个
  - definition.function: fn activate(&mut self) {: 1 个
  - definition.function: fn activate_user(&self, id: UserId) -> Result<()> {: 1 个
  - definition.function: fn clone(&self) -> Self {: 1 个
  - definition.function: fn closure_examples() {: 1 个
  - definition.function: fn create_user(&self, name: UserName, email: String) -> Result<UserId> {: 1 个
  - definition.function: fn divide(a: f64, b: f64) -> Result<f64> {: 1 个
  - definition.function: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>: 1 个
  - definition.function: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {: 1 个
  - definition.function: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {: 2 个
  - definition.function: fn generate_user_id() -> UserId {: 1 个
  - definition.function: fn generate_verification_code() -> String {: 1 个
  - definition.function: fn get(&self, id: &UserId) -> Option<&T> {: 1 个
  - definition.function: fn get_user(&self, id: UserId) -> Option<User> {: 1 个
  - definition.function: fn insert(&mut self, id: UserId, item: T) -> Result<()> {: 1 个
  - definition.function: fn into_name(self) -> UserName {: 1 个
  - definition.function: fn is_active(&self) -> bool {: 1 个
  - definition.function: fn len(&self) -> usize {: 1 个
  - definition.function: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {: 1 个
  - definition.function: fn main() -> Result<()> {: 1 个
  - definition.function: fn new() -> Self {: 1 个
  - definition.function: fn new(id: UserId, name: UserName, email: String) -> Self {: 1 个
  - definition.function: fn new(max_size: usize) -> Self {: 1 个
  - definition.function: fn process_user_status(status: &UserStatus) -> String {: 1 个
  - definition.function: fn remove(&mut self, id: &UserId) -> Option<T> {: 1 个
  - definition.function: fn serialize(&self) -> String {: 1 个
  - definition.function: fn test_repository_operations() {: 1 个
  - definition.function: fn test_user_activation() {: 1 个
  - definition.function: fn test_user_creation() {: 1 个
  - definition.function: fn validate(&self) -> Result<()> {: 1 个
  - definition.method_container: impl<T: Clone> Clone for Repository<T> {: 1 个
  - definition.method_container: impl<T> Repository<T> {: 1 个
  - definition.struct: struct Repository<T> {: 1 个
  - definition.struct: struct User {: 1 个
  - definition.struct: struct UserService {: 1 个

代码块信息 (11 个):
  块 1: 第 3-25 行 (23 行)
      3: use std::collections::HashMap;
      4: use std::fmt::{Display, Formatter, Result as FmtResult};
      5: use std::sync::{Arc, Mutex};
    ... (还有 20 行)

  块 2: 第 27-47 行 (21 行)
     27: #[derive(Debug)]
     28: enum DatabaseError {
     29:     ConnectionFailed,
    ... (还有 18 行)

  块 3: 第 48-84 行 (37 行)
     48: struct User {
     49:     id: UserId,
     50:     name: UserName,
    ... (还有 34 行)

  块 4: 第 86-126 行 (41 行)
     86: impl Display for User {
     87:     fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
     88:         write!(f, "User(id: {}, name: {}, status: {:?})", self.id, self.name, self.status)
    ... (还有 38 行)

  块 5: 第 129-153 行 (25 行)
    129: trait Validate {
    130:     fn validate(&self) -> Result<()>;
    131: }
    ... (还有 22 行)

  块 6: 第 156-220 行 (65 行)
    156: impl<T: Clone> Clone for Repository<T> {
    157:     fn clone(&self) -> Self {
    158:         Self {
    ... (还有 62 行)

  块 7: 第 223-250 行 (28 行)
    223: fn generate_user_id() -> UserId {
    224:     use std::sync::atomic::{AtomicU64, Ordering};
    225:     static COUNTER: AtomicU64 = AtomicU64::new(1);
    ... (还有 25 行)

  块 8: 第 253-278 行 (26 行)
    253: fn closure_examples() {
    254:     let numbers = vec![1, 2, 3, 4, 5];
    255:     
    ... (还有 23 行)

  块 9: 第 281-302 行 (22 行)
    281: fn divide(a: f64, b: f64) -> Result<f64> {
    282:     if b == 0.0 {
    283:         Err("Division by zero".into())
    ... (还有 19 行)

  块 10: 第 303-330 行 (28 行)
    303: mod tests {
    304:     use super::*;
    305:     
    ... (还有 25 行)

  块 11: 第 333-379 行 (47 行)
    333: fn main() -> Result<()> {
    334:     log_info!("Starting user service");
    335:     
    ... (还有 44 行)


统计信息:
  覆盖率: 95.5%
  块中总行数: 363
  结构类型数: 37
