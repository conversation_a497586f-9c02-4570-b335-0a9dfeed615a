FileParser 解析结果报告 - SCALA
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala
  文件名: sample.scala
  内容长度: 12951 字符
  行数: 434

关键结构行 (38 个):
  第   3 行: definition.namespace: package com.example.sample
           内容: package com.example.sample
  第  12 行: definition.class: case class User(
           内容: case class User(
  第  56 行: definition.method: def fromString(value: String): Option[UserStatus] = value.toLowerCase match {
           内容: def fromString(value: String): Option[UserStatus] = value.toLowerCase match {
  第  97 行: definition.class: abstract class BaseRepository[T, ID] {
           内容: abstract class BaseRepository[T, ID] {
  第 102 行: definition.method: def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {
           内容: def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {
  第 113 行: definition.class: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {
           内容: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {
  第 116 行: definition.method: override def findById(id: Long): Future[Option[User]] = Future {
           内容: override def findById(id: Long): Future[Option[User]] = Future {
  第 120 行: definition.method: override def save(user: User): Future[Boolean] = Future {
           内容: override def save(user: User): Future[Boolean] = Future {
  第 125 行: definition.method: override def delete(id: Long): Future[Boolean] = Future {
           内容: override def delete(id: Long): Future[Boolean] = Future {
  第 129 行: definition.method: override def findAll(): Future[List[User]] = Future {
           内容: override def findAll(): Future[List[User]] = Future {
  第 133 行: definition.method: override def findByStatus(status: UserStatus): Future[List[User]] = Future {
           内容: override def findByStatus(status: UserStatus): Future[List[User]] = Future {
  第 138 行: definition.class: class UserValidator extends Validator[User] {
           内容: class UserValidator extends Validator[User] {
  第 139 行: definition.method: override def validate(item: User): List[ValidationError] = {
           内容: override def validate(item: User): List[ValidationError] = {
  第 155 行: definition.method: private def isValidEmail(email: String): Boolean = {
           内容: private def isValidEmail(email: String): Boolean = {
  第 161 行: definition.class: class ConsoleLogger extends Logger {
           内容: class ConsoleLogger extends Logger {
  第 162 行: definition.method: override def info(message: String): Unit = {
           内容: override def info(message: String): Unit = {
  第 166 行: definition.method: override def error(message: String, throwable: Option[Throwable] = None): Unit = {
           内容: override def error(message: String, throwable: Option[Throwable] = None): Unit = {
  第 171 行: definition.method: override def debug(message: String): Unit = {
           内容: override def debug(message: String): Unit = {
  第 176 行: definition.class: class UserService(
           内容: class UserService(
  第 184 行: definition.method: def createUser(name: String, email: String): Future[Result[User]] = {
           内容: def createUser(name: String, email: String): Future[Result[User]] = {
  第 214 行: definition.method: def activateUser(id: Long): Future[Boolean] = {
           内容: def activateUser(id: Long): Future[Boolean] = {
  第 234 行: definition.method: def getUserStats(): Future[Map[UserStatus, Int]] = {
           内容: def getUserStats(): Future[Map[UserStatus, Int]] = {
  第 244 行: definition.method: def addEventListener(listener: UserEvent => Unit): Unit = {
           内容: def addEventListener(listener: UserEvent => Unit): Unit = {
  第 248 行: definition.method: private def notifyListeners(event: UserEvent): Unit = {
           内容: private def notifyListeners(event: UserEvent): Unit = {
  第 257 行: definition.method: def createSampleUser(): User = User(
           内容: def createSampleUser(): User = User(
  第 263 行: definition.method: def createUsers(count: Int): List[User] = (1 to count).map { i =>
           内容: def createUsers(count: Int): List[User] = (1 to count).map { i =>
  第 279 行: definition.class: class ApiClient private(baseUrl: String, timeoutMs: Long) {
           内容: class ApiClient private(baseUrl: String, timeoutMs: Long) {
  第 295 行: definition.class: implicit class UserOps(user: User) {
           内容: implicit class UserOps(user: User) {
  第 296 行: definition.method: def toJson: String = {
           内容: def toJson: String = {
  第 301 行: definition.method: def isOlderThan(days: Int): Boolean = {
           内容: def isOlderThan(days: Int): Boolean = {
  第 306 行: definition.class: implicit class UserListOps(users: List[User]) {
           内容: implicit class UserListOps(users: List[User]) {
  第 316 行: definition.method: def measureTime[T](operation: () => T): (T, Long) = {
           内容: def measureTime[T](operation: () => T): (T, Long) = {
  第 323 行: definition.method: def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T])
           内容: def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T])
  第 325 行: definition.method: def attempt(remaining: Int): Future[T] = {
           内容: def attempt(remaining: Int): Future[T] = {
  第 336 行: definition.method: def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {
           内容: def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {
  第 343 行: definition.method: def processResult[T](result: Result[T]): String = result match {
           内容: def processResult[T](result: Result[T]): String = result match {
  第 350 行: definition.method: def processUser(user: User): String = user match {
           内容: def processUser(user: User): String = user match {
  第 357 行: definition.method: def extractUserInfo(user: User): (String, String) = user match {
           内容: def extractUserInfo(user: User): (String, String) = user match {

检测到的结构类型:
  - definition.class: abstract class BaseRepository[T, ID] {: 1 个
  - definition.class: case class User(: 1 个
  - definition.class: class ApiClient private(baseUrl: String, timeoutMs: Long) {: 1 个
  - definition.class: class ConsoleLogger extends Logger {: 1 个
  - definition.class: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {: 1 个
  - definition.class: class UserService(: 1 个
  - definition.class: class UserValidator extends Validator[User] {: 1 个
  - definition.class: implicit class UserListOps(users: List[User]) {: 1 个
  - definition.class: implicit class UserOps(user: User) {: 1 个
  - definition.method: def activateUser(id: Long): Future[Boolean] = {: 1 个
  - definition.method: def addEventListener(listener: UserEvent => Unit): Unit = {: 1 个
  - definition.method: def attempt(remaining: Int): Future[T] = {: 1 个
  - definition.method: def createSampleUser(): User = User(: 1 个
  - definition.method: def createUser(name: String, email: String): Future[Result[User]] = {: 1 个
  - definition.method: def createUsers(count: Int): List[User] = (1 to count).map { i =>: 1 个
  - definition.method: def extractUserInfo(user: User): (String, String) = user match {: 1 个
  - definition.method: def fromString(value: String): Option[UserStatus] = value.toLowerCase match {: 1 个
  - definition.method: def getUserStats(): Future[Map[UserStatus, Int]] = {: 1 个
  - definition.method: def isOlderThan(days: Int): Boolean = {: 1 个
  - definition.method: def measureTime[T](operation: () => T): (T, Long) = {: 1 个
  - definition.method: def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {: 1 个
  - definition.method: def processResult[T](result: Result[T]): String = result match {: 1 个
  - definition.method: def processUser(user: User): String = user match {: 1 个
  - definition.method: def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T]): 1 个
  - definition.method: def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {: 1 个
  - definition.method: def toJson: String = {: 1 个
  - definition.method: override def debug(message: String): Unit = {: 1 个
  - definition.method: override def delete(id: Long): Future[Boolean] = Future {: 1 个
  - definition.method: override def error(message: String, throwable: Option[Throwable] = None): Unit = {: 1 个
  - definition.method: override def findAll(): Future[List[User]] = Future {: 1 个
  - definition.method: override def findById(id: Long): Future[Option[User]] = Future {: 1 个
  - definition.method: override def findByStatus(status: UserStatus): Future[List[User]] = Future {: 1 个
  - definition.method: override def info(message: String): Unit = {: 1 个
  - definition.method: override def save(user: User): Future[Boolean] = Future {: 1 个
  - definition.method: override def validate(item: User): List[ValidationError] = {: 1 个
  - definition.method: private def isValidEmail(email: String): Boolean = {: 1 个
  - definition.method: private def notifyListeners(event: UserEvent): Unit = {: 1 个
  - definition.namespace: package com.example.sample: 1 个

代码块信息 (10 个):
  块 1: 第 3-28 行 (26 行)
      3: package com.example.sample
      4: 
      5: import scala.concurrent.{Future, ExecutionContext}
    ... (还有 23 行)

  块 2: 第 30-63 行 (34 行)
     30: case class UserStats(posts: Int = 0, followers: Int = 0, following: Int = 0)
     31: 
     32: case class ValidationError(field: String, message: String)
    ... (还有 31 行)

  块 3: 第 65-89 行 (25 行)
     65: sealed trait Result[+T]
     66: case class Success[T](data: T) extends Result[T]
     67: case class Error(message: String, cause: Option[Throwable] = None) extends Result[Nothing]
    ... (还有 22 行)

  块 4: 第 91-136 行 (46 行)
     91: trait Validator[T] {
     92:   def validate(item: T): List[ValidationError]
     93:   def isValid(item: T): Boolean = validate(item).isEmpty
    ... (还有 43 行)

  块 5: 第 138-159 行 (22 行)
    138: class UserValidator extends Validator[User] {
    139:   override def validate(item: User): List[ValidationError] = {
    140:     val errors = mutable.ListBuffer[ValidationError]()
    ... (还有 19 行)

  块 6: 第 161-253 行 (93 行)
    161: class ConsoleLogger extends Logger {
    162:   override def info(message: String): Unit = {
    163:     println(s"[${LocalDateTime.now()}] [INFO] $message")
    ... (还有 90 行)

  块 7: 第 256-276 行 (21 行)
    256: object UserFactory {
    257:   def createSampleUser(): User = User(
    258:     id = 1L,
    ... (还有 18 行)

  块 8: 第 279-304 行 (26 行)
    279: class ApiClient private(baseUrl: String, timeoutMs: Long) {
    280:   // Implementation would go here
    281: }
    ... (还有 23 行)

  块 9: 第 306-339 行 (34 行)
    306: implicit class UserListOps(users: List[User]) {
    307:   def activeUsers: List[User] = users.filter(_.isActive)
    308:   
    ... (还有 31 行)

  块 10: 第 342-433 行 (92 行)
    342: object PatternMatchingExamples {
    343:   def processResult[T](result: Result[T]): String = result match {
    344:     case Success(data) => s"Success: $data"
    ... (还有 89 行)


统计信息:
  覆盖率: 96.5%
  块中总行数: 419
  结构类型数: 38
