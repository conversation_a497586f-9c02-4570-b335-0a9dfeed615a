FileParser 解析结果报告 - SCALA
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala
  文件名: sample.scala
  内容长度: 12951 字符
  行数: 434

关键结构行 (17 个):
  第  12 行: definition.class: case class User(
           内容: case class User(
  第  30 行: definition.class: case class UserStats(posts: Int = 0, followers: Int = 0, following: Int = 0)
           内容: case class UserStats(posts: Int = 0, followers: Int = 0, following: Int = 0)
  第  32 行: definition.class: case class ValidationError(field: String, message: String)
           内容: case class ValidationError(field: String, message: String)
  第  66 行: definition.class: case class Success[T](data: T) extends Result[T]
           内容: case class Success[T](data: T) extends Result[T]
  第  67 行: definition.class: case class Error(message: String, cause: Option[Throwable] = None) extends Result[Nothing]
           内容: case class Error(message: String, cause: Option[Throwable] = None) extends Result[Nothing]
  第  71 行: definition.class: case class UserCreated(user: User) extends UserEvent
           内容: case class UserCreated(user: User) extends UserEvent
  第  72 行: definition.class: case class UserUpdated(user: User) extends UserEvent
           内容: case class UserUpdated(user: User) extends UserEvent
  第  73 行: definition.class: case class UserDeleted(userId: Long) extends UserEvent
           内容: case class UserDeleted(userId: Long) extends UserEvent
  第  74 行: definition.class: case class UserStatusChanged(userId: Long, newStatus: UserStatus) extends UserEvent
           内容: case class UserStatusChanged(userId: Long, newStatus: UserStatus) extends UserEvent
  第  97 行: definition.class: abstract class BaseRepository[T, ID] {
           内容: abstract class BaseRepository[T, ID] {
  第 113 行: definition.class: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {
           内容: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {
  第 138 行: definition.class: class UserValidator extends Validator[User] {
           内容: class UserValidator extends Validator[User] {
  第 161 行: definition.class: class ConsoleLogger extends Logger {
           内容: class ConsoleLogger extends Logger {
  第 176 行: definition.class: class UserService(
           内容: class UserService(
  第 279 行: definition.class: class ApiClient private(baseUrl: String, timeoutMs: Long) {
           内容: class ApiClient private(baseUrl: String, timeoutMs: Long) {
  第 295 行: definition.class: implicit class UserOps(user: User) {
           内容: implicit class UserOps(user: User) {
  第 306 行: definition.class: implicit class UserListOps(users: List[User]) {
           内容: implicit class UserListOps(users: List[User]) {

检测到的结构类型:
  - definition.class: abstract class BaseRepository[T, ID] {: 1 个
  - definition.class: case class Error(message: String, cause: Option[Throwable] = None) extends Result[Nothing]: 1 个
  - definition.class: case class Success[T](data: T) extends Result[T]: 1 个
  - definition.class: case class User(: 1 个
  - definition.class: case class UserCreated(user: User) extends UserEvent: 1 个
  - definition.class: case class UserDeleted(userId: Long) extends UserEvent: 1 个
  - definition.class: case class UserStats(posts: Int = 0, followers: Int = 0, following: Int = 0): 1 个
  - definition.class: case class UserStatusChanged(userId: Long, newStatus: UserStatus) extends UserEvent: 1 个
  - definition.class: case class UserUpdated(user: User) extends UserEvent: 1 个
  - definition.class: case class ValidationError(field: String, message: String): 1 个
  - definition.class: class ApiClient private(baseUrl: String, timeoutMs: Long) {: 1 个
  - definition.class: class ConsoleLogger extends Logger {: 1 个
  - definition.class: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {: 1 个
  - definition.class: class UserService(: 1 个
  - definition.class: class UserValidator extends Validator[User] {: 1 个
  - definition.class: implicit class UserListOps(users: List[User]) {: 1 个
  - definition.class: implicit class UserOps(user: User) {: 1 个

代码块信息 (35 个):
  块 1: 第 3-28 行 (26 行)
      3: package com.example.sample
      4: 
      5: import scala.concurrent.{Future, ExecutionContext}
    ... (还有 23 行)

  块 2: 第 21-32 行 (12 行)
     21:   def isActive: Boolean = status == UserStatus.Active
     22:   
     23:   def displayName: String = if (name.trim.isEmpty) "Unknown User" else name
    ... (还有 9 行)

  块 3: 第 32-63 行 (32 行)
     32: case class ValidationError(field: String, message: String)
     33: 
     34: // Sealed traits and classes
    ... (还有 29 行)

  块 4: 第 40-50 行 (11 行)
     40:   case object Active extends UserStatus {
     41:     def displayName: String = "Active"
     42:   }
    ... (还有 8 行)

  块 5: 第 49-62 行 (14 行)
     49:     def displayName: String = "Suspended"
     50:   }
     51:   
    ... (还有 11 行)

  块 6: 第 65-77 行 (13 行)
     65: sealed trait Result[+T]
     66: case class Success[T](data: T) extends Result[T]
     67: case class Error(message: String, cause: Option[Throwable] = None) extends Result[Nothing]
    ... (还有 10 行)

  块 7: 第 77-89 行 (13 行)
     77: trait UserRepository {
     78:   def findById(id: Long): Future[Option[User]]
     79:   def save(user: User): Future[Boolean]
    ... (还有 10 行)

  块 8: 第 91-110 行 (20 行)
     91: trait Validator[T] {
     92:   def validate(item: T): List[ValidationError]
     93:   def isValid(item: T): Boolean = validate(item).isEmpty
    ... (还有 17 行)

  块 9: 第 102-113 行 (12 行)
    102:   def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {
    103:     doSave(item).map { success =>
    104:       if (success) Success(item)
    ... (还有 9 行)

  块 10: 第 114-125 行 (12 行)
    114:   private val users = new ConcurrentHashMap[Long, User]()
    115:   
    116:   override def findById(id: Long): Future[Option[User]] = Future {
    ... (还有 9 行)

  块 11: 第 125-135 行 (11 行)
    125:   override def delete(id: Long): Future[Boolean] = Future {
    126:     users.remove(id) != null
    127:   }
    ... (还有 8 行)

  块 12: 第 138-159 行 (22 行)
    138: class UserValidator extends Validator[User] {
    139:   override def validate(item: User): List[ValidationError] = {
    140:     val errors = mutable.ListBuffer[ValidationError]()
    ... (还有 19 行)

  块 13: 第 139-153 行 (15 行)
    139:   override def validate(item: User): List[ValidationError] = {
    140:     val errors = mutable.ListBuffer[ValidationError]()
    141:     
    ... (还有 12 行)

  块 14: 第 140-155 行 (16 行)
    140:     val errors = mutable.ListBuffer[ValidationError]()
    141:     
    142:     if (item.name.trim.isEmpty) {
    ... (还有 13 行)

  块 15: 第 155-174 行 (20 行)
    155:   private def isValidEmail(email: String): Boolean = {
    156:     val emailRegex = """[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}""".r
    157:     emailRegex.matches(email)
    ... (还有 17 行)

  块 16: 第 162-173 行 (12 行)
    162:   override def info(message: String): Unit = {
    163:     println(s"[${LocalDateTime.now()}] [INFO] $message")
    164:   }
    ... (还有 9 行)

  块 17: 第 176-176 行 (1 行)
    176: class UserService(

  块 18: 第 176-212 行 (37 行)
    176: class UserService(
    177:   repository: UserRepository,
    178:   validator: Validator[User],
    ... (还有 34 行)

  块 19: 第 185-214 行 (30 行)
    185:     val user = User(
    186:       id = generateUserId(),
    187:       name = name,
    ... (还有 27 行)

  块 20: 第 217-234 行 (18 行)
    217:         val activatedUser = user.activate
    218:         repository.save(activatedUser).map { success =>
    219:           if (success) {
    ... (还有 15 行)

  块 21: 第 234-244 行 (11 行)
    234:   def getUserStats(): Future[Map[UserStatus, Int]] = {
    235:     repository.findAll().map { users =>
    236:       users.groupBy(_.status).view.mapValues(_.size).toMap
    ... (还有 8 行)

  块 22: 第 244-256 行 (13 行)
    244:   def addEventListener(listener: UserEvent => Unit): Unit = {
    245:     eventListeners += listener
    246:   }
    ... (还有 10 行)

  块 23: 第 257-269 行 (13 行)
    257:   def createSampleUser(): User = User(
    258:     id = 1L,
    259:     name = "John Doe",
    ... (还有 10 行)

  块 24: 第 272-283 行 (12 行)
    272: object Constants {
    273:   val MaxUsers = 1000
    274:   val DefaultPageSize = 20
    ... (还有 9 行)

  块 25: 第 284-295 行 (12 行)
    284:   private val DefaultBaseUrl = "https://api.example.com"
    285:   private val DefaultTimeout = 30000L
    286:   
    ... (还有 9 行)

  块 26: 第 296-306 行 (11 行)
    296:   def toJson: String = {
    297:     // Simplified JSON serialization
    298:     s"""{"id":${user.id},"name":"${user.name}","email":"${user.email}","status":"${user.status}"}"""
    ... (还有 8 行)

  块 27: 第 306-339 行 (34 行)
    306: implicit class UserListOps(users: List[User]) {
    307:   def activeUsers: List[User] = users.filter(_.isActive)
    308:   
    ... (还有 31 行)

  块 28: 第 316-334 行 (19 行)
    316:   def measureTime[T](operation: () => T): (T, Long) = {
    317:     val startTime = System.currentTimeMillis()
    318:     val result = operation()
    ... (还有 16 行)

  块 29: 第 325-336 行 (12 行)
    325:     def attempt(remaining: Int): Future[T] = {
    326:       operation().recoverWith {
    327:         case ex if remaining > 1 =>
    ... (还有 9 行)

  块 30: 第 336-360 行 (25 行)
    336:   def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {
    337:     items.grouped(batchSize).map(processor).toList
    338:   }
    ... (还有 22 行)

  块 31: 第 343-355 行 (13 行)
    343:   def processResult[T](result: Result[T]): String = result match {
    344:     case Success(data) => s"Success: $data"
    345:     case Error(message, Some(cause)) => s"Error: $message (caused by: ${cause.getMessage})"
    ... (还有 10 行)

  块 32: 第 357-357 行 (1 行)
    357:   def extractUserInfo(user: User): (String, String) = user match {

  块 33: 第 363-380 行 (18 行)
    363: object UserApp extends App {
    364:   implicit val ec: ExecutionContext = ExecutionContext.global
    365:   
    ... (还有 15 行)

  块 34: 第 413-423 行 (11 行)
    413:   val sampleUsers = UserFactory.createUsers(5)
    414:   sampleUsers.foreach { user =>
    415:     repository.save(user)
    ... (还有 8 行)

  块 35: 第 423-426 行 (4 行)
    423:   val batches = UserUtils.processInBatches(sampleUsers, 2) { batch =>
    424:     logger.info(s"Processing batch with ${batch.size} users")
    425:     batch.map(_.name)
    ... (还有 1 行)


统计信息:
  覆盖率: 128.1%
  块中总行数: 556
  结构类型数: 17
