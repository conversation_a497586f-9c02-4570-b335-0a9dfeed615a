FileParser 解析结果报告 - SWIFT
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift
  文件名: sample.swift
  内容长度: 8711 字符
  行数: 337

关键结构行 (10 个):
  第   7 行: definition.interface: protocol UserRepositoryProtocol {
           内容: protocol UserRepositoryProtocol {
  第  13 行: definition.interface: protocol Validatable {
           内容: protocol Validatable {
  第  19 行: definition.class: enum UserStatus: String, CaseIterable {
           内容: enum UserStatus: String, CaseIterable {
  第  39 行: definition.class: enum NetworkError: Error {
           内容: enum NetworkError: Error {
  第  47 行: definition.class: struct User: Codable, Validatable {
           内容: struct User: Codable, Validatable {
  第 116 行: definition.class: class UserRepository: UserRepositoryProtocol {
           内容: class UserRepository: UserRepositoryProtocol {
  第 148 行: definition.class: class UserService {
           内容: class UserService {
  第 191 行: definition.class: class Repository<T: Codable> {
           内容: class Repository<T: Codable> {
  第 251 行: definition.class: class Logger {
           内容: class Logger {
  第 252 行: definition.class: enum Level: String {
           内容: enum Level: String {

检测到的结构类型:
  - definition.class: class Logger {: 1 个
  - definition.class: class Repository<T: Codable> {: 1 个
  - definition.class: class UserRepository: UserRepositoryProtocol {: 1 个
  - definition.class: class UserService {: 1 个
  - definition.class: enum Level: String {: 1 个
  - definition.class: enum NetworkError: Error {: 1 个
  - definition.class: enum UserStatus: String, CaseIterable {: 1 个
  - definition.class: struct User: Codable, Validatable {: 1 个
  - definition.interface: protocol UserRepositoryProtocol {: 1 个
  - definition.interface: protocol Validatable {: 1 个

代码块信息 (20 个):
  块 1: 第 7-37 行 (31 行)
      7: protocol UserRepositoryProtocol {
      8:     func findUser(by id: Int) -> User?
      9:     func saveUser(_ user: User) -> Bool
    ... (还有 28 行)

  块 2: 第 39-44 行 (6 行)
     39: enum NetworkError: Error {
     40:     case invalidURL
     41:     case noData
    ... (还有 3 行)

  块 3: 第 47-59 行 (13 行)
     47: struct User: Codable, Validatable {
     48:     let id: Int
     49:     var name: String
    ... (还有 10 行)

  块 4: 第 61-74 行 (14 行)
     61:     var displayName: String {
     62:         return name.isEmpty ? "Unknown User" : name
     63:     }
    ... (还有 11 行)

  块 5: 第 77-102 行 (26 行)
     77:     mutating func activate() {
     78:         status = .active
     79:         updatedAt = Date()
    ... (还有 23 行)

  块 6: 第 89-106 行 (18 行)
     89:         var errors: [String] = []
     90:         
     91:         if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
    ... (还有 15 行)

  块 7: 第 108-146 行 (39 行)
    108:     private func isValidEmail(_ email: String) -> Bool {
    109:         let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
    110:         let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
    ... (还有 36 行)

  块 8: 第 117-133 行 (17 行)
    117:     private var users: [Int: User] = [:]
    118:     private let queue = DispatchQueue(label: "userRepository", attributes: .concurrent)
    119:     
    ... (还有 14 行)

  块 9: 第 135-145 行 (11 行)
    135:     func deleteUser(by id: Int) -> Bool {
    136:         return queue.sync(flags: .barrier) {
    137:             return users.removeValue(forKey: id) != nil
    ... (还有 8 行)

  块 10: 第 149-173 行 (25 行)
    149:     private let repository: UserRepositoryProtocol
    150:     private let logger: Logger
    151:     
    ... (还有 22 行)

  块 11: 第 158-183 行 (26 行)
    158:         let user = User(id: generateUserId(), name: name, email: email)
    159:         
    160:         guard user.isValid else {
    ... (还有 23 行)

  块 12: 第 185-218 行 (34 行)
    185:     private func generateUserId() -> Int {
    186:         return Int.random(in: 1...1000000)
    187:     }
    ... (还有 31 行)

  块 13: 第 192-205 行 (14 行)
    192:     private var items: [String: T] = [:]
    193:     private let queue = DispatchQueue(label: "repository", attributes: .concurrent)
    194:     
    ... (还有 11 行)

  块 14: 第 207-217 行 (11 行)
    207:     func delete(by key: String) -> Bool {
    208:         return queue.sync(flags: .barrier) {
    209:             return items.removeValue(forKey: key) != nil
    ... (还有 8 行)

  块 15: 第 222-232 行 (11 行)
    222:     static func createSampleUser() -> User {
    223:         return User(id: 1, name: "John Doe", email: "<EMAIL>")
    224:     }
    ... (还有 8 行)

  块 16: 第 227-247 行 (21 行)
    227:         let encoder = JSONEncoder()
    228:         encoder.dateEncodingStrategy = .iso8601
    229:         
    ... (还有 18 行)

  块 17: 第 252-264 行 (13 行)
    252:     enum Level: String {
    253:         case info = "INFO"
    254:         case warning = "WARNING"
    ... (还有 10 行)

  块 18: 第 266-281 行 (16 行)
    266:     func error(_ message: String) {
    267:         log(message, level: .error)
    268:     }
    ... (还有 13 行)

  块 19: 第 278-294 行 (17 行)
    278:     let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
    279:     let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
    280:     return emailPredicate.evaluate(with: email)
    ... (还有 14 行)

  块 20: 第 298-320 行 (23 行)
    298:     let logger = Logger()
    299:     let repository = UserRepository()
    300:     let service = UserService(repository: repository, logger: logger)
    ... (还有 20 行)


统计信息:
  覆盖率: 114.5%
  块中总行数: 386
  结构类型数: 10
