FileParser 解析结果报告 - SWIFT
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift
  文件名: sample.swift
  内容长度: 8711 字符
  行数: 337

关键结构行 (34 个):
  第   7 行: definition.interface: protocol UserRepositoryProtocol {
           内容: protocol UserRepositoryProtocol {
  第  13 行: definition.interface: protocol Validatable {
           内容: protocol Validatable {
  第  19 行: definition.class: enum UserStatus: String, CaseIterable {
           内容: enum UserStatus: String, CaseIterable {
  第  39 行: definition.class: enum NetworkError: Error {
           内容: enum NetworkError: Error {
  第  47 行: definition.class: struct User: Codable, Validatable {
           内容: struct User: Codable, Validatable {
  第  77 行: definition.method: mutating func activate() {
           内容: mutating func activate() {
  第  82 行: definition.method: mutating func updateName(_ newName: String) {
           内容: mutating func updateName(_ newName: String) {
  第  88 行: definition.method: func validate() -> [String] {
           内容: func validate() -> [String] {
  第 108 行: definition.method: private func isValidEmail(_ email: String) -> Bool {
           内容: private func isValidEmail(_ email: String) -> Bool {
  第 116 行: definition.class: class UserRepository: UserRepositoryProtocol {
           内容: class UserRepository: UserRepositoryProtocol {
  第 120 行: definition.method: func findUser(by id: Int) -> User? {
           内容: func findUser(by id: Int) -> User? {
  第 126 行: definition.method: func saveUser(_ user: User) -> Bool {
           内容: func saveUser(_ user: User) -> Bool {
  第 135 行: definition.method: func deleteUser(by id: Int) -> Bool {
           内容: func deleteUser(by id: Int) -> Bool {
  第 141 行: definition.method: func getAllUsers() -> [User] {
           内容: func getAllUsers() -> [User] {
  第 148 行: definition.class: class UserService {
           内容: class UserService {
  第 157 行: definition.method: func createUser(name: String, email: String) -> Result<User, NetworkError> {
           内容: func createUser(name: String, email: String) -> Result<User, NetworkError> {
  第 175 行: definition.method: func activateUser(id: Int) -> Bool {
           内容: func activateUser(id: Int) -> Bool {
  第 185 行: definition.method: private func generateUserId() -> Int {
           内容: private func generateUserId() -> Int {
  第 191 行: definition.class: class Repository<T: Codable> {
           内容: class Repository<T: Codable> {
  第 195 行: definition.method: func save(_ item: T, with key: String) {
           内容: func save(_ item: T, with key: String) {
  第 201 行: definition.method: func find(by key: String) -> T? {
           内容: func find(by key: String) -> T? {
  第 207 行: definition.method: func delete(by key: String) -> Bool {
           内容: func delete(by key: String) -> Bool {
  第 213 行: definition.method: func all() -> [T] {
           内容: func all() -> [T] {
  第 222 行: definition.method: static func createSampleUser() -> User {
           内容: static func createSampleUser() -> User {
  第 226 行: definition.method: func toJSON() -> String? {
           内容: func toJSON() -> String? {
  第 251 行: definition.class: class Logger {
           内容: class Logger {
  第 252 行: definition.class: enum Level: String {
           内容: enum Level: String {
  第 258 行: definition.method: func info(_ message: String) {
           内容: func info(_ message: String) {
  第 262 行: definition.method: func warning(_ message: String) {
           内容: func warning(_ message: String) {
  第 266 行: definition.method: func error(_ message: String) {
           内容: func error(_ message: String) {
  第 270 行: definition.method: private func log(_ message: String, level: Level) {
           内容: private func log(_ message: String, level: Level) {
  第 277 行: definition.method: func validateEmail(_ email: String) -> Bool {
           内容: func validateEmail(_ email: String) -> Bool {
  第 283 行: definition.method: func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {
           内容: func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {
  第 297 行: definition.method: func exampleUsage() {
           内容: func exampleUsage() {

检测到的结构类型:
  - definition.class: class Logger {: 1 个
  - definition.class: class Repository<T: Codable> {: 1 个
  - definition.class: class UserRepository: UserRepositoryProtocol {: 1 个
  - definition.class: class UserService {: 1 个
  - definition.class: enum Level: String {: 1 个
  - definition.class: enum NetworkError: Error {: 1 个
  - definition.class: enum UserStatus: String, CaseIterable {: 1 个
  - definition.class: struct User: Codable, Validatable {: 1 个
  - definition.interface: protocol UserRepositoryProtocol {: 1 个
  - definition.interface: protocol Validatable {: 1 个
  - definition.method: func activateUser(id: Int) -> Bool {: 1 个
  - definition.method: func all() -> [T] {: 1 个
  - definition.method: func createUser(name: String, email: String) -> Result<User, NetworkError> {: 1 个
  - definition.method: func delete(by key: String) -> Bool {: 1 个
  - definition.method: func deleteUser(by id: Int) -> Bool {: 1 个
  - definition.method: func error(_ message: String) {: 1 个
  - definition.method: func exampleUsage() {: 1 个
  - definition.method: func find(by key: String) -> T? {: 1 个
  - definition.method: func findUser(by id: Int) -> User? {: 1 个
  - definition.method: func getAllUsers() -> [User] {: 1 个
  - definition.method: func info(_ message: String) {: 1 个
  - definition.method: func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {: 1 个
  - definition.method: func save(_ item: T, with key: String) {: 1 个
  - definition.method: func saveUser(_ user: User) -> Bool {: 1 个
  - definition.method: func toJSON() -> String? {: 1 个
  - definition.method: func validate() -> [String] {: 1 个
  - definition.method: func validateEmail(_ email: String) -> Bool {: 1 个
  - definition.method: func warning(_ message: String) {: 1 个
  - definition.method: mutating func activate() {: 1 个
  - definition.method: mutating func updateName(_ newName: String) {: 1 个
  - definition.method: private func generateUserId() -> Int {: 1 个
  - definition.method: private func isValidEmail(_ email: String) -> Bool {: 1 个
  - definition.method: private func log(_ message: String, level: Level) {: 1 个
  - definition.method: static func createSampleUser() -> User {: 1 个

代码块信息 (18 个):
  块 1: 第 7-37 行 (31 行)
      7: protocol UserRepositoryProtocol {
      8:     func findUser(by id: Int) -> User?
      9:     func saveUser(_ user: User) -> Bool
    ... (还有 28 行)

  块 2: 第 25-113 行 (89 行)
     25:     var displayName: String {
     26:         switch self {
     27:         case .active:
    ... (还有 86 行)

  块 3: 第 48-74 行 (27 行)
     48:     let id: Int
     49:     var name: String
     50:     var email: String
    ... (还有 24 行)

  块 4: 第 77-102 行 (26 行)
     77:     mutating func activate() {
     78:         status = .active
     79:         updatedAt = Date()
    ... (还有 23 行)

  块 5: 第 89-112 行 (24 行)
     89:         var errors: [String] = []
     90:         
     91:         if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
    ... (还有 21 行)

  块 6: 第 109-146 行 (38 行)
    109:         let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
    110:         let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
    111:         return emailPredicate.evaluate(with: email)
    ... (还有 35 行)

  块 7: 第 117-139 行 (23 行)
    117:     private var users: [Int: User] = [:]
    118:     private let queue = DispatchQueue(label: "userRepository", attributes: .concurrent)
    119:     
    ... (还有 20 行)

  块 8: 第 141-188 行 (48 行)
    141:     func getAllUsers() -> [User] {
    142:         return queue.sync {
    143:             return Array(users.values)
    ... (还有 45 行)

  块 9: 第 149-173 行 (25 行)
    149:     private let repository: UserRepositoryProtocol
    150:     private let logger: Logger
    151:     
    ... (还有 22 行)

  块 10: 第 158-183 行 (26 行)
    158:         let user = User(id: generateUserId(), name: name, email: email)
    159:         
    160:         guard user.isValid else {
    ... (还有 23 行)

  块 11: 第 185-218 行 (34 行)
    185:     private func generateUserId() -> Int {
    186:         return Int.random(in: 1...1000000)
    187:     }
    ... (还有 31 行)

  块 12: 第 192-217 行 (26 行)
    192:     private var items: [String: T] = [:]
    193:     private let queue = DispatchQueue(label: "repository", attributes: .concurrent)
    194:     
    ... (还有 23 行)

  块 13: 第 222-247 行 (26 行)
    222:     static func createSampleUser() -> User {
    223:         return User(id: 1, name: "John Doe", email: "<EMAIL>")
    224:     }
    ... (还有 23 行)

  块 14: 第 251-274 行 (24 行)
    251: class Logger {
    252:     enum Level: String {
    253:         case info = "INFO"
    ... (还有 21 行)

  块 15: 第 252-273 行 (22 行)
    252:     enum Level: String {
    253:         case info = "INFO"
    254:         case warning = "WARNING"
    ... (还有 19 行)

  块 16: 第 271-294 行 (24 行)
    271:         let timestamp = DateFormatter.iso8601.string(from: Date())
    272:         print("[\(timestamp)] [\(level.rawValue)] \(message)")
    273:     }
    ... (还有 21 行)

  块 17: 第 297-331 行 (35 行)
    297: func exampleUsage() {
    298:     let logger = Logger()
    299:     let repository = UserRepository()
    ... (还有 32 行)

  块 18: 第 298-320 行 (23 行)
    298:     let logger = Logger()
    299:     let repository = UserRepository()
    300:     let service = UserService(repository: repository, logger: logger)
    ... (还有 20 行)


统计信息:
  覆盖率: 169.4%
  块中总行数: 571
  结构类型数: 34
