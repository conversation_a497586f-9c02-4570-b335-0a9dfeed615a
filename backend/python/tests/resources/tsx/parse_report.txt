FileParser 解析结果报告 - TSX
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx
  文件名: sample.tsx
  内容长度: 13859 字符
  行数: 512

关键结构行 (7 个):
  第   9 行: name.definition.interface: interface User {
           内容: interface User {
  第  20 行: name.definition.interface: interface UserStats {
           内容: interface UserStats {
  第  27 行: name.definition.interface: interface UserPreferences {
           内容: interface UserPreferences {
  第  37 行: name.definition.interface: interface FormErrors {
           内容: interface FormErrors {
  第  42 行: name.definition.interface: interface UserProfileProps {
           内容: interface UserProfileProps {
  第  49 行: name.definition.interface: interface UserStatsProps {
           内容: interface UserStatsProps {
  第  54 行: name.definition.interface: interface LoadingProps {
           内容: interface LoadingProps {

检测到的结构类型:
  - name.definition.interface: interface FormErrors {: 1 个
  - name.definition.interface: interface LoadingProps {: 1 个
  - name.definition.interface: interface User {: 1 个
  - name.definition.interface: interface UserPreferences {: 1 个
  - name.definition.interface: interface UserProfileProps {: 1 个
  - name.definition.interface: interface UserStats {: 1 个
  - name.definition.interface: interface UserStatsProps {: 1 个

代码块信息 (52 个):
  块 1: 第 9-25 行 (17 行)
      9: interface User {
     10:   id: number;
     11:   name: string;
    ... (还有 14 行)

  块 2: 第 20-31 行 (12 行)
     20: interface UserStats {
     21:   posts: number;
     22:   followers: number;
    ... (还有 9 行)

  块 3: 第 27-39 行 (13 行)
     27: interface UserPreferences {
     28:   theme: 'light' | 'dark';
     29:   notifications: boolean;
    ... (还有 10 行)

  块 4: 第 37-47 行 (11 行)
     37: interface FormErrors {
     38:   [key: string]: string | null;
     39: }
    ... (还有 8 行)

  块 5: 第 42-52 行 (11 行)
     42: interface UserProfileProps {
     43:   user: User | null;
     44:   onUpdate: (data: FormData) => Promise<void>;
    ... (还有 8 行)

  块 6: 第 49-60 行 (12 行)
     49: interface UserStatsProps {
     50:   user: User;
     51:   compact?: boolean;
    ... (还有 9 行)

  块 7: 第 64-79 行 (16 行)
     64:   background-color: ${props => props.theme === 'dark' ? '#333' : '#fff'};
     65:   color: ${props => props.theme === 'dark' ? '#fff' : '#333'};
     66: `;
    ... (还有 13 行)

  块 8: 第 74-88 行 (15 行)
     74:     switch (props.variant) {
     75:       case 'primary': return '#007bff';
     76:       case 'danger': return '#dc3545';
    ... (还有 12 行)

  块 9: 第 83-94 行 (12 行)
     83:     switch (props.size) {
     84:       case 'small': return '5px 10px';
     85:       case 'large': return '15px 30px';
    ... (还有 9 行)

  块 10: 第 94-109 行 (16 行)
     94:     opacity: ${props => props.disabled ? 0.6 : 0.8};
     95:   }
     96: `;
    ... (还有 13 行)

  块 11: 第 105-138 行 (34 行)
    105: type AsyncState<T> = {
    106:   data: T | null;
    107:   loading: boolean;
    ... (还有 31 行)

  块 12: 第 123-133 行 (11 行)
    123:     setState(prev => ({ ...prev, loading: true, error: null }));
    124:     
    125:     try {
    ... (还有 8 行)

  块 13: 第 132-160 行 (29 行)
    132:         error: error instanceof Error ? error.message : 'Unknown error' 
    133:       }));
    134:     }
    ... (还有 26 行)

  块 14: 第 147-182 行 (36 行)
    147:     const fetchUser = async (): Promise<User> => {
    148:       const response = await fetch(`/api/users/${userId}`);
    149:       if (!response.ok) {
    ... (还有 33 行)

  块 15: 第 174-180 行 (7 行)
    174:     <div className="loading-container">
    175:       <div 
    176:         className="spinner" 
    ... (还有 4 行)

  块 16: 第 185-210 行 (26 行)
    185: const UserProfile: React.FC<UserProfileProps> = ({ 
    186:   user, 
    187:   onUpdate, 
    ... (还有 23 行)

  块 17: 第 205-217 行 (13 行)
    205:         name: user.name,
    206:         email: user.email,
    207:         bio: user.bio || ''
    ... (还有 10 行)

  块 18: 第 214-234 行 (21 行)
    214:     if (isEditing && inputRef.current) {
    215:       inputRef.current.focus();
    216:     }
    ... (还有 18 行)

  块 19: 第 223-240 行 (18 行)
    223:     if (!data.name.trim()) {
    224:       newErrors.name = 'Name is required';
    225:     }
    ... (还有 15 行)

  块 20: 第 239-252 行 (14 行)
    239:     return Object.keys(validationErrors).length === 0;
    240:   }, [formData, validateForm]);
    241: 
    ... (还有 11 行)

  块 21: 第 244-276 行 (33 行)
    244:     (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    245:       const value = event.target.value;
    246:       setFormData(prev => ({ ...prev, [field]: value }));
    ... (还有 30 行)

  块 22: 第 256-271 行 (16 行)
    256:     event.preventDefault();
    257:     
    258:     const validationErrors = validateForm(formData);
    ... (还有 13 行)

  块 23: 第 283-295 行 (13 行)
    283:         name: user.name,
    284:         email: user.email,
    285:         bio: user.bio || ''
    ... (还有 10 行)

  块 24: 第 293-293 行 (1 行)
    293:       <Container theme="light" className={className}>

  块 25: 第 300-397 行 (98 行)
    300:     <Container theme={user.preferences.theme} className={className}>
    301:       <div className="user-profile">
    302:         <div className="profile-header">
    ... (还有 95 行)

  块 26: 第 301-396 行 (96 行)
    301:       <div className="user-profile">
    302:         <div className="profile-header">
    303:           <img 
    ... (还有 93 行)

  块 27: 第 302-393 行 (92 行)
    302:         <div className="profile-header">
    303:           <img 
    304:             src={user.avatar || '/default-avatar.png'} 
    ... (还有 89 行)

  块 28: 第 309-392 行 (84 行)
    309:           <div className="user-info">
    310:             {isEditing ? (
    311:               <form onSubmit={handleSubmit} className="edit-form">
    ... (还有 81 行)

  块 29: 第 310-391 行 (82 行)
    310:             {isEditing ? (
    311:               <form onSubmit={handleSubmit} className="edit-form">
    312:                 <div className="form-group">
    ... (还有 79 行)

  块 30: 第 311-326 行 (16 行)
    311:               <form onSubmit={handleSubmit} className="edit-form">
    312:                 <div className="form-group">
    313:                   <label htmlFor="name">Name:</label>
    ... (还有 13 行)

  块 31: 第 313-323 行 (11 行)
    313:                   <label htmlFor="name">Name:</label>
    314:                   <input
    315:                     ref={inputRef}
    ... (还有 8 行)

  块 32: 第 324-341 行 (18 行)
    324:                     <span className="error-message">{errors.name}</span>
    325:                   )}
    326:                 </div>
    ... (还有 15 行)

  块 33: 第 329-339 行 (11 行)
    329:                   <label htmlFor="email">Email:</label>
    330:                   <input
    331:                     id="email"
    ... (还有 8 行)

  块 34: 第 339-352 行 (14 行)
    339:                     <span className="error-message">{errors.email}</span>
    340:                   )}
    341:                 </div>
    ... (还有 11 行)

  块 35: 第 344-354 行 (11 行)
    344:                   <label htmlFor="bio">Bio:</label>
    345:                   <textarea
    346:                     id="bio"
    ... (还有 8 行)

  块 36: 第 355-373 行 (19 行)
    355:                   <div className="error-message">{errors.submit}</div>
    356:                 )}
    357:                 
    ... (还有 16 行)

  块 37: 第 359-372 行 (14 行)
    359:                   <Button 
    360:                     type="submit" 
    361:                     variant="primary" 
    ... (还有 11 行)

  块 38: 第 368-390 行 (23 行)
    368:                     onClick={handleCancel}
    369:                     disabled={isSubmitting}
    370:                   >
    ... (还有 20 行)

  块 39: 第 377-388 行 (12 行)
    377:                 <h2>{user.name}</h2>
    378:                 <p className="email">{user.email}</p>
    379:                 <p className="role">Role: {user.role}</p>
    ... (还有 9 行)

  块 40: 第 384-395 行 (12 行)
    384:                     onClick={() => setIsEditing(true)} 
    385:                     variant="primary"
    386:                   >
    ... (还有 9 行)

  块 41: 第 395-440 行 (46 行)
    395:         {user.stats && <UserStatsComponent user={user} />}
    396:       </div>
    397:     </Container>
    ... (还有 43 行)

  块 42: 第 405-438 行 (34 行)
    405:   const toggleExpanded = useCallback(() => {
    406:     setExpanded(prev => !prev);
    407:   }, []);
    ... (还有 31 行)

  块 43: 第 413-436 行 (24 行)
    413:       <h3 onClick={toggleExpanded} className="stats-header">
    414:         User Statistics {expanded ? '▼' : '▶'}
    415:       </h3>
    ... (还有 21 行)

  块 44: 第 418-429 行 (12 行)
    418:           <div className="stat-item">
    419:             <span className="stat-label">Posts:</span>
    420:             <span className="stat-value">{user.stats.posts}</span>
    ... (还有 9 行)

  块 45: 第 427-427 行 (1 行)
    427:             <span className="stat-label">Following:</span>

  块 46: 第 443-457 行 (15 行)
    443: const App: React.FC = () => {
    444:   const [selectedUserId, setSelectedUserId] = useState<number | null>(1);
    445:   const userState = useUserData(selectedUserId);
    ... (还有 12 行)

  块 47: 第 451-461 行 (11 行)
    451:       body: JSON.stringify(updatedData)
    452:     });
    453:     
    ... (还有 8 行)

  块 48: 第 463-474 行 (12 行)
    463:   if (userState.error) {
    464:     return (
    465:       <div className="error-container">
    ... (还有 9 行)

  块 49: 第 466-505 行 (40 行)
    466:         <h2>Error</h2>
    467:         <p>{userState.error}</p>
    468:         <Button 
    ... (还有 37 行)

  块 50: 第 481-492 行 (12 行)
    481:         <h1>User Profile App</h1>
    482:         <nav>
    483:           {[1, 2, 3].map(userId => (
    ... (还有 9 行)

  块 51: 第 483-504 行 (22 行)
    483:           {[1, 2, 3].map(userId => (
    484:             <Button 
    485:               key={userId}
    ... (还有 19 行)

  块 52: 第 496-496 行 (1 行)
    496:         {userState.loading ? (


统计信息:
  覆盖率: 244.1%
  块中总行数: 1250
  结构类型数: 7
