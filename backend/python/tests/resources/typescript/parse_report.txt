FileParser 解析结果报告 - TYPESCRIPT
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts
  文件名: sample.ts
  内容长度: 4984 字符
  行数: 212

关键结构行 (21 个):
  第  10 行: name.definition.interface: interface User {
           内容: interface User {
  第  18 行: name.definition.interface: interface ApiResponse<T> {
           内容: interface ApiResponse<T> {
  第  25 行: name.definition.interface: interface Repository<T> {
           内容: interface Repository<T> {
  第  32 行: name.definition.class: class UserService implements Repository<User> {
           内容: class UserService implements Repository<User> {
  第  57 行: name.definition.class: abstract class Shape {
           内容: abstract class Shape {
  第  69 行: name.definition.class: class Circle extends Shape {
           内容: class Circle extends Shape {
  第  84 行: name.definition.enum: enum Status {
           内容: enum Status {
  第  90 行: name.definition.enum: enum Direction {
           内容: enum Direction {
  第  98 行: name.definition.function: function processData<T>(
           内容: function processData<T>(
  第 121 行: name.definition.function: function combine(a: string, b: string): string;
           内容: function combine(a: string, b: string): string;
  第 122 行: name.definition.function: function combine(a: number, b: number): number;
           内容: function combine(a: number, b: number): number;
  第 123 行: name.definition.function: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {
           内容: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {
  第 129 行: name.definition.function: export function formatDate(date: Date): string {
           内容: export function formatDate(date: Date): string {
  第 133 行: name.definition.function: export function isValidEmail(email: string): boolean {
           内容: export function isValidEmail(email: string): boolean {
  第 137 行: name.definition.class: export class Logger {
           内容: export class Logger {
  第 146 行: name.definition.interface: interface Window {
           内容: interface Window {
  第 169 行: name.definition.function: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {
           内容: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  第 180 行: name.definition.class: class Calculator {
           内容: class Calculator {
  第 193 行: name.definition.function: function isString(value: any): value is string {
           内容: function isString(value: any): value is string {
  第 197 行: name.definition.function: function isUser(obj: any): obj is User {
           内容: function isUser(obj: any): obj is User {
  第 202 行: name.definition.function: function assertIsNumber(value: any): asserts value is number {
           内容: function assertIsNumber(value: any): asserts value is number {

检测到的结构类型:
  - name.definition.class: abstract class Shape {: 1 个
  - name.definition.class: class Calculator {: 1 个
  - name.definition.class: class Circle extends Shape {: 1 个
  - name.definition.class: class UserService implements Repository<User> {: 1 个
  - name.definition.class: export class Logger {: 1 个
  - name.definition.enum: enum Direction {: 1 个
  - name.definition.enum: enum Status {: 1 个
  - name.definition.function: export function formatDate(date: Date): string {: 1 个
  - name.definition.function: export function isValidEmail(email: string): boolean {: 1 个
  - name.definition.function: function assertIsNumber(value: any): asserts value is number {: 1 个
  - name.definition.function: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {: 1 个
  - name.definition.function: function combine(a: number, b: number): number;: 1 个
  - name.definition.function: function combine(a: string, b: string): string;: 1 个
  - name.definition.function: function isString(value: any): value is string {: 1 个
  - name.definition.function: function isUser(obj: any): obj is User {: 1 个
  - name.definition.function: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {: 1 个
  - name.definition.function: function processData<T>(: 1 个
  - name.definition.interface: interface ApiResponse<T> {: 1 个
  - name.definition.interface: interface Repository<T> {: 1 个
  - name.definition.interface: interface User {: 1 个
  - name.definition.interface: interface Window {: 1 个

代码块信息 (24 个):
  块 1: 第 6-16 行 (11 行)
      6: type StringOrNumber = string | number;
      7: type UserRole = 'admin' | 'user' | 'guest';
      8: 
    ... (还有 8 行)

  块 2: 第 10-22 行 (13 行)
     10: interface User {
     11:     id: number;
     12:     name: string;
    ... (还有 10 行)

  块 3: 第 18-29 行 (12 行)
     18: interface ApiResponse<T> {
     19:     data: T;
     20:     status: number;
    ... (还有 9 行)

  块 4: 第 25-54 行 (30 行)
     25: interface Repository<T> {
     26:     findById(id: number): Promise<T | null>;
     27:     save(entity: T): Promise<T>;
    ... (还有 27 行)

  块 5: 第 32-44 行 (13 行)
     32: class UserService implements Repository<User> {
     33:     private users: User[] = [];
     34:     
    ... (还有 10 行)

  块 6: 第 41-53 行 (13 行)
     41:     async save(user: User): Promise<User> {
     42:         this.users.push(user);
     43:         return user;
    ... (还有 10 行)

  块 7: 第 51-66 行 (16 行)
     51:     async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {
     52:         return this.users.filter(user => user[field] === value);
     53:     }
    ... (还有 13 行)

  块 8: 第 57-81 行 (25 行)
     57: abstract class Shape {
     58:     protected constructor(protected color: string) {}
     59:     
    ... (还有 22 行)

  块 9: 第 69-80 行 (12 行)
     69: class Circle extends Shape {
     70:     constructor(color: string, private radius: number) {
     71:         super(color);
    ... (还有 9 行)

  块 10: 第 78-88 行 (11 行)
     78:     getPerimeter(): number {
     79:         return 2 * Math.PI * this.radius;
     80:     }
    ... (还有 8 行)

  块 11: 第 84-95 行 (12 行)
     84: enum Status {
     85:     PENDING = 'pending',
     86:     APPROVED = 'approved',
    ... (还有 9 行)

  块 12: 第 90-110 行 (21 行)
     90: enum Direction {
     91:     UP,
     92:     DOWN,
    ... (还有 18 行)

  块 13: 第 98-115 行 (18 行)
     98: function processData<T>(
     99:     data: T[],
    100:     predicate: (item: T) => boolean,
    ... (还有 15 行)

  块 14: 第 114-125 行 (12 行)
    114:     return items.reduce((total, item) => total + (item.price * item.quantity), 0);
    115: };
    116: 
    ... (还有 9 行)

  块 15: 第 123-142 行 (20 行)
    123: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {
    124:     return (a as any) + (b as any);
    125: }
    ... (还有 17 行)

  块 16: 第 128-141 行 (14 行)
    128: namespace Utils {
    129:     export function formatDate(date: Date): string {
    130:         return date.toISOString().split('T')[0];
    ... (还有 11 行)

  块 17: 第 137-148 行 (12 行)
    137:     export class Logger {
    138:         static log(message: string): void {
    139:             console.log(`[${new Date().toISOString()}] ${message}`);
    ... (还有 9 行)

  块 18: 第 146-158 行 (13 行)
    146:     interface Window {
    147:         customProperty: string;
    148:     }
    ... (还有 10 行)

  块 19: 第 156-166 行 (11 行)
    156: type Partial<T> = {
    157:     [P in keyof T]?: T[P];
    158: };
    ... (还有 8 行)

  块 20: 第 166-178 行 (13 行)
    166: type RequiredUser = Required<User>;
    167: 
    168: // Decorators (experimental)
    ... (还有 10 行)

  块 21: 第 169-190 行 (22 行)
    169: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    170:     const method = descriptor.value;
    171:     
    ... (还有 19 行)

  块 22: 第 180-195 行 (16 行)
    180: class Calculator {
    181:     @logged
    182:     add(a: number, b: number): number {
    ... (还有 13 行)

  块 23: 第 193-206 行 (14 行)
    193: function isString(value: any): value is string {
    194:     return typeof value === 'string';
    195: }
    ... (还有 11 行)

  块 24: 第 202-202 行 (1 行)
    202: function assertIsNumber(value: any): asserts value is number {


统计信息:
  覆盖率: 167.5%
  块中总行数: 355
  结构类型数: 21
