FileParser 解析结果报告 - TYPESCRIPT
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts
  文件名: sample.ts
  内容长度: 4984 字符
  行数: 212

关键结构行 (40 个):
  第  10 行: definition.interface: interface User {
           内容: interface User {
  第  18 行: definition.interface: interface ApiResponse<T> {
           内容: interface ApiResponse<T> {
  第  25 行: definition.interface: interface Repository<T> {
           内容: interface Repository<T> {
  第  26 行: definition.method: findById(id: number): Promise<T | null>;
           内容: findById(id: number): Promise<T | null>;
  第  27 行: definition.method: save(entity: T): Promise<T>;
           内容: save(entity: T): Promise<T>;
  第  28 行: definition.method: delete(id: number): Promise<void>;
           内容: delete(id: number): Promise<void>;
  第  32 行: definition.class: class UserService implements Repository<User> {
           内容: class UserService implements Repository<User> {
  第  35 行: definition.method: constructor(private apiUrl: string) {}
           内容: constructor(private apiUrl: string) {}
  第  37 行: definition.method: async findById(id: number): Promise<User | null> {
           内容: async findById(id: number): Promise<User | null> {
  第  41 行: definition.method: async save(user: User): Promise<User> {
           内容: async save(user: User): Promise<User> {
  第  46 行: definition.method: async delete(id: number): Promise<void> {
           内容: async delete(id: number): Promise<void> {
  第  51 行: definition.method: async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {
           内容: async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {
  第  57 行: definition.class: abstract class Shape {
           内容: abstract class Shape {
  第  58 行: definition.method: protected constructor(protected color: string) {}
           内容: protected constructor(protected color: string) {}
  第  60 行: definition.method: abstract getArea(): number;
           内容: abstract getArea(): number;
  第  61 行: definition.method: abstract getPerimeter(): number;
           内容: abstract getPerimeter(): number;
  第  63 行: definition.method: getColor(): string {
           内容: getColor(): string {
  第  69 行: definition.class: class Circle extends Shape {
           内容: class Circle extends Shape {
  第  70 行: definition.method: constructor(color: string, private radius: number) {
           内容: constructor(color: string, private radius: number) {
  第  74 行: definition.method: getArea(): number {
           内容: getArea(): number {
  第  78 行: definition.method: getPerimeter(): number {
           内容: getPerimeter(): number {
  第  84 行: definition.enum: enum Status {
           内容: enum Status {
  第  90 行: definition.enum: enum Direction {
           内容: enum Direction {
  第  98 行: definition.function: function processData<T>(
           内容: function processData<T>(
  第 121 行: definition.function: function combine(a: string, b: string): string;
           内容: function combine(a: string, b: string): string;
  第 122 行: definition.function: function combine(a: number, b: number): number;
           内容: function combine(a: number, b: number): number;
  第 123 行: definition.function: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {
           内容: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {
  第 128 行: definition.namespace: namespace Utils {
           内容: namespace Utils {
  第 129 行: definition.function: export function formatDate(date: Date): string {
           内容: export function formatDate(date: Date): string {
  第 133 行: definition.function: export function isValidEmail(email: string): boolean {
           内容: export function isValidEmail(email: string): boolean {
  第 137 行: definition.class: export class Logger {
           内容: export class Logger {
  第 138 行: definition.method: static log(message: string): void {
           内容: static log(message: string): void {
  第 146 行: definition.interface: interface Window {
           内容: interface Window {
  第 169 行: definition.function: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {
           内容: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  第 180 行: definition.class: class Calculator {
           内容: class Calculator {
  第 182 行: definition.method: add(a: number, b: number): number {
           内容: add(a: number, b: number): number {
  第 187 行: definition.method: multiply(a: number, b: number): number {
           内容: multiply(a: number, b: number): number {
  第 193 行: definition.function: function isString(value: any): value is string {
           内容: function isString(value: any): value is string {
  第 197 行: definition.function: function isUser(obj: any): obj is User {
           内容: function isUser(obj: any): obj is User {
  第 202 行: definition.function: function assertIsNumber(value: any): asserts value is number {
           内容: function assertIsNumber(value: any): asserts value is number {

检测到的结构类型:
  - definition.class: abstract class Shape {: 1 个
  - definition.class: class Calculator {: 1 个
  - definition.class: class Circle extends Shape {: 1 个
  - definition.class: class UserService implements Repository<User> {: 1 个
  - definition.class: export class Logger {: 1 个
  - definition.enum: enum Direction {: 1 个
  - definition.enum: enum Status {: 1 个
  - definition.function: export function formatDate(date: Date): string {: 1 个
  - definition.function: export function isValidEmail(email: string): boolean {: 1 个
  - definition.function: function assertIsNumber(value: any): asserts value is number {: 1 个
  - definition.function: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {: 1 个
  - definition.function: function combine(a: number, b: number): number;: 1 个
  - definition.function: function combine(a: string, b: string): string;: 1 个
  - definition.function: function isString(value: any): value is string {: 1 个
  - definition.function: function isUser(obj: any): obj is User {: 1 个
  - definition.function: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {: 1 个
  - definition.function: function processData<T>(: 1 个
  - definition.interface: interface ApiResponse<T> {: 1 个
  - definition.interface: interface Repository<T> {: 1 个
  - definition.interface: interface User {: 1 个
  - definition.interface: interface Window {: 1 个
  - definition.method: abstract getArea(): number;: 1 个
  - definition.method: abstract getPerimeter(): number;: 1 个
  - definition.method: add(a: number, b: number): number {: 1 个
  - definition.method: async delete(id: number): Promise<void> {: 1 个
  - definition.method: async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {: 1 个
  - definition.method: async findById(id: number): Promise<User | null> {: 1 个
  - definition.method: async save(user: User): Promise<User> {: 1 个
  - definition.method: constructor(color: string, private radius: number) {: 1 个
  - definition.method: constructor(private apiUrl: string) {}: 1 个
  - definition.method: delete(id: number): Promise<void>;: 1 个
  - definition.method: findById(id: number): Promise<T | null>;: 1 个
  - definition.method: getArea(): number {: 1 个
  - definition.method: getColor(): string {: 1 个
  - definition.method: getPerimeter(): number {: 1 个
  - definition.method: multiply(a: number, b: number): number {: 1 个
  - definition.method: protected constructor(protected color: string) {}: 1 个
  - definition.method: save(entity: T): Promise<T>;: 1 个
  - definition.method: static log(message: string): void {: 1 个
  - definition.namespace: namespace Utils {: 1 个

代码块信息 (8 个):
  块 1: 第 6-29 行 (24 行)
      6: type StringOrNumber = string | number;
      7: type UserRole = 'admin' | 'user' | 'guest';
      8: 
    ... (还有 21 行)

  块 2: 第 32-54 行 (23 行)
     32: class UserService implements Repository<User> {
     33:     private users: User[] = [];
     34:     
    ... (还有 20 行)

  块 3: 第 57-81 行 (25 行)
     57: abstract class Shape {
     58:     protected constructor(protected color: string) {}
     59:     
    ... (还有 22 行)

  块 4: 第 84-110 行 (27 行)
     84: enum Status {
     85:     PENDING = 'pending',
     86:     APPROVED = 'approved',
    ... (还有 24 行)

  块 5: 第 113-142 行 (30 行)
    113: const calculateTotal = (items: { price: number; quantity: number }[]): number => {
    114:     return items.reduce((total, item) => total + (item.price * item.quantity), 0);
    115: };
    ... (还有 27 行)

  块 6: 第 146-166 行 (21 行)
    146:     interface Window {
    147:         customProperty: string;
    148:     }
    ... (还有 18 行)

  块 7: 第 169-190 行 (22 行)
    169: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    170:     const method = descriptor.value;
    171:     
    ... (还有 19 行)

  块 8: 第 193-206 行 (14 行)
    193: function isString(value: any): value is string {
    194:     return typeof value === 'string';
    195: }
    ... (还有 11 行)


统计信息:
  覆盖率: 87.7%
  块中总行数: 186
  结构类型数: 40
