{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "filename": "sample.ts", "content_length": 4984, "line_count": 212}, "parsing_results": {"key_structure_lines": {"10": "definition.interface: interface User {", "18": "definition.interface: interface ApiResponse<T> {", "25": "definition.interface: interface Repository<T> {", "26": "definition.method: findById(id: number): Promise<T | null>;", "27": "definition.method: save(entity: T): Promise<T>;", "28": "definition.method: delete(id: number): Promise<void>;", "32": "definition.class: class UserService implements Repository<User> {", "35": "definition.method: constructor(private apiUrl: string) {}", "37": "definition.method: async findById(id: number): Promise<User | null> {", "41": "definition.method: async save(user: User): Promise<User> {", "46": "definition.method: async delete(id: number): Promise<void> {", "51": "definition.method: async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {", "57": "definition.class: abstract class Shape {", "58": "definition.method: protected constructor(protected color: string) {}", "60": "definition.method: abstract getArea(): number;", "61": "definition.method: abstract getPerimeter(): number;", "63": "definition.method: getColor(): string {", "69": "definition.class: class Circle extends Shape {", "70": "definition.method: constructor(color: string, private radius: number) {", "74": "definition.method: getArea(): number {", "78": "definition.method: getPerimeter(): number {", "84": "definition.enum: enum Status {", "90": "definition.enum: enum Direction {", "98": "definition.function: function processData<T>(", "121": "definition.function: function combine(a: string, b: string): string;", "122": "definition.function: function combine(a: number, b: number): number;", "123": "definition.function: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {", "128": "definition.namespace: namespace Utils {", "129": "definition.function: export function formatDate(date: Date): string {", "133": "definition.function: export function isValidEmail(email: string): boolean {", "137": "definition.class: export class Logger {", "138": "definition.method: static log(message: string): void {", "146": "definition.interface: interface Window {", "169": "definition.function: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {", "180": "definition.class: class Calculator {", "182": "definition.method: add(a: number, b: number): number {", "187": "definition.method: multiply(a: number, b: number): number {", "193": "definition.function: function isString(value: any): value is string {", "197": "definition.function: function isUser(obj: any): obj is User {", "202": "definition.function: function assertIsNumber(value: any): asserts value is number {"}, "key_structure_count": 40, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "start_line": 6, "end_line": 29, "content": "type StringOrNumber = string | number;\ntype UserRole = 'admin' | 'user' | 'guest';\n\n// Interface declarations\ninterface User {\n    id: number;\n    name: string;\n    email?: string;\n    role: UserRole;\n    readonly createdAt: Date;\n}\n\ninterface ApiResponse<T> {\n    data: T;\n    status: number;\n    message: string;\n}\n\n// Generic interface\ninterface Repository<T> {\n    findById(id: number): Promise<T | null>;\n    save(entity: T): Promise<T>;\n    delete(id: number): Promise<void>;\n}", "line_count": 24}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "start_line": 32, "end_line": 54, "content": "class UserService implements Repository<User> {\n    private users: User[] = [];\n    \n    constructor(private apiUrl: string) {}\n    \n    async findById(id: number): Promise<User | null> {\n        return this.users.find(user => user.id === id) || null;\n    }\n    \n    async save(user: User): Promise<User> {\n        this.users.push(user);\n        return user;\n    }\n    \n    async delete(id: number): Promise<void> {\n        this.users = this.users.filter(user => user.id !== id);\n    }\n    \n    // Method with generic type parameter\n    async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {\n        return this.users.filter(user => user[field] === value);\n    }\n}", "line_count": 23}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "start_line": 57, "end_line": 81, "content": "abstract class Shape {\n    protected constructor(protected color: string) {}\n    \n    abstract getArea(): number;\n    abstract getPerimeter(): number;\n    \n    getColor(): string {\n        return this.color;\n    }\n}\n\n// Concrete class extending abstract class\nclass Circle extends Shape {\n    constructor(color: string, private radius: number) {\n        super(color);\n    }\n    \n    getArea(): number {\n        return Math.PI * this.radius ** 2;\n    }\n    \n    getPerimeter(): number {\n        return 2 * Math.PI * this.radius;\n    }\n}", "line_count": 25}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "start_line": 84, "end_line": 110, "content": "enum Status {\n    PENDING = 'pending',\n    APPROVED = 'approved',\n    REJECTED = 'rejected'\n}\n\nenum Direction {\n    UP,\n    DOWN,\n    LEFT,\n    RIGHT\n}\n\n// Function with type annotations\nfunction processData<T>(\n    data: T[],\n    predicate: (item: T) => boolean,\n    transformer?: (item: T) => T\n): T[] {\n    let filtered = data.filter(predicate);\n    \n    if (transformer) {\n        filtered = filtered.map(transformer);\n    }\n    \n    return filtered;\n}", "line_count": 27}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "start_line": 113, "end_line": 142, "content": "const calculateTotal = (items: { price: number; quantity: number }[]): number => {\n    return items.reduce((total, item) => total + (item.price * item.quantity), 0);\n};\n\n// Generic function\nconst identity = <T>(arg: T): T => arg;\n\n// Function overloads\nfunction combine(a: string, b: string): string;\nfunction combine(a: number, b: number): number;\nfunction combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {\n    return (a as any) + (b as any);\n}\n\n// Namespace\nnamespace Utils {\n    export function formatDate(date: Date): string {\n        return date.toISOString().split('T')[0];\n    }\n    \n    export function isValidEmail(email: string): boolean {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n    }\n    \n    export class Logger {\n        static log(message: string): void {\n            console.log(`[${new Date().toISOString()}] ${message}`);\n        }\n    }\n}", "line_count": 30}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "start_line": 146, "end_line": 166, "content": "    interface Window {\n        customProperty: string;\n    }\n}\n\n// Conditional types\ntype NonNullable<T> = T extends null | undefined ? never : T;\ntype ReturnType<T> = T extends (...args: any[]) => infer R ? R : any;\n\n// Mapped types\ntype Partial<T> = {\n    [P in keyof T]?: T[P];\n};\n\ntype Required<T> = {\n    [P in keyof T]-?: T[P];\n};\n\n// Utility types usage\ntype PartialUser = Partial<User>;\ntype RequiredUser = Required<User>;", "line_count": 21}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "start_line": 169, "end_line": 190, "content": "function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {\n    const method = descriptor.value;\n    \n    descriptor.value = function (...args: any[]) {\n        console.log(`Calling ${propertyName} with args:`, args);\n        const result = method.apply(this, args);\n        console.log(`Result:`, result);\n        return result;\n    };\n}\n\nclass Calculator {\n    @logged\n    add(a: number, b: number): number {\n        return a + b;\n    }\n    \n    @logged\n    multiply(a: number, b: number): number {\n        return a * b;\n    }\n}", "line_count": 22}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/typescript/sample.ts", "start_line": 193, "end_line": 206, "content": "function isString(value: any): value is string {\n    return typeof value === 'string';\n}\n\nfunction isUser(obj: any): obj is User {\n    return obj && typeof obj.id === 'number' && typeof obj.name === 'string';\n}\n\n// Assertion functions\nfunction assertIsNumber(value: any): asserts value is number {\n    if (typeof value !== 'number') {\n        throw new Error('Expected number');\n    }\n}", "line_count": 14}], "chunk_count": 8}, "analysis": {"detected_structures": ["definition.function: export function isValidEmail(email: string): boolean {", "definition.function: export function formatDate(date: Date): string {", "definition.class: abstract class Shape {", "definition.function: function isString(value: any): value is string {", "definition.interface: interface User {", "definition.method: getColor(): string {", "definition.function: function combine(a: number, b: number): number;", "definition.function: function processData<T>(", "definition.method: getArea(): number {", "definition.interface: interface Window {", "definition.method: async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {", "definition.class: class UserService implements Repository<User> {", "definition.interface: interface ApiResponse<T> {", "definition.function: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {", "definition.method: static log(message: string): void {", "definition.namespace: namespace Utils {", "definition.function: function isUser(obj: any): obj is User {", "definition.enum: enum Direction {", "definition.enum: enum Status {", "definition.function: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {", "definition.class: export class Logger {", "definition.method: getPerimeter(): number {", "definition.method: multiply(a: number, b: number): number {", "definition.method: async save(user: User): Promise<User> {", "definition.method: constructor(color: string, private radius: number) {", "definition.method: add(a: number, b: number): number {", "definition.function: function combine(a: string, b: string): string;", "definition.method: protected constructor(protected color: string) {}", "definition.class: class Calculator {", "definition.method: findById(id: number): Promise<T | null>;", "definition.method: async delete(id: number): Promise<void> {", "definition.method: constructor(private apiUrl: string) {}", "definition.method: abstract getPerimeter(): number;", "definition.function: function assertIsNumber(value: any): asserts value is number {", "definition.method: async findById(id: number): Promise<User | null> {", "definition.method: abstract getArea(): number;", "definition.method: save(entity: T): Promise<T>;", "definition.class: class Circle extends Shape {", "definition.method: delete(id: number): Promise<void>;", "definition.interface: interface Repository<T> {"], "structure_types_count": 40, "total_lines_in_chunks": 186, "coverage_percentage": 87.73584905660378}}